{"key": "container_layout_1726799513409_1", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-1", "name": "基本信息", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1747116386459_29", "name": "专利名称", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747116413378_30", "name": "专利类型", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利类型", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747117721651_0", "label": "发明", "value": "invention"}, {"_id": "1747117724487_1", "label": "实用新型", "value": "utility_model"}, {"_id": "1747117730936_2", "label": "外观设计", "value": "design"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747116495821_2"}], "display_configurable_form": {}}, "model_key": "patent_type", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747116416227_31", "name": "授权公告日/公开日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的授权公告日/公开日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "pub_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747116419291_32", "name": "申请日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的申请日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116432207_33", "name": "申请号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的申请号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116435272_34", "name": "主分类号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的主分类号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "ipc", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116441798_37", "name": "专利权人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利权人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "patentee", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116439482_36", "name": "发明人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的发明人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "inventors", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116446198_39", "name": "联系人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116447997_40", "name": "联系电话", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系电话", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_mobile", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116437522_35", "name": "专利代理机构", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "agency", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747116444066_38", "name": "代理机构代码", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "agency_code", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747116465711_42", "name": "专利权人状态", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利权人状态", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747126273131_0", "label": "唯一", "value": "唯一"}, {"_id": "1747126279605_1", "label": "院企合作", "value": "院企合作"}, {"_id": "1747126286169_2", "label": "院校合作", "value": "院校合作"}, {"_id": "1747126288715_3", "label": "其它合作", "value": "其它合作"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747116515079_3"}], "display_configurable_form": {}}, "model_key": "patentee_state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747116467700_43", "name": "专利法律状态", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利法律状态", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747126119446_0", "label": "有效", "value": "valid"}, {"_id": "1747126120545_1", "label": "无效", "value": "invalid"}, {"_id": "1747126121652_2", "label": "再审", "value": "review"}, {"_id": "1747126122476_3", "label": "待定", "value": "pending"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747124723903_7"}], "display_configurable_form": {}}, "model_key": "legal_state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747116460526_41", "name": "专利权状态", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的专利权状态", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747126171992_4", "label": "维持", "value": "维持"}, {"_id": "1747126173989_5", "label": "申请未授权", "value": "申请未授权"}, {"_id": "1747126188793_6", "label": "届满失效", "value": "届满失效"}, {"_id": "1747126197353_7", "label": "未缴纳年费失效", "value": "未缴纳年费失效"}, {"_id": "1747126199074_8", "label": "放弃", "value": "放弃"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747117788559_6"}], "display_configurable_form": {}}, "model_key": "state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747116470050_44", "name": "专利权失效日期", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的专利权失效日期", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "expire_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747116478968_45", "name": "转化意向", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化意向", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747117361683_0", "label": "有意向", "value": "有意向"}, {"_id": "1747117369755_1", "label": "无意向", "value": "无意向"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747116522188_4"}], "display_configurable_form": {}}, "model_key": "convert_intention", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747116481698_46", "name": "转化方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747126584661_0", "label": "转让", "value": "转让"}, {"_id": "1747126605040_1", "label": "许可", "value": "许可"}, {"_id": "1747126612215_2", "label": "作价", "value": "作价"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747117530938_5"}], "display_configurable_form": {}}, "model_key": "convert_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "file_1747116543015_48", "name": "专利证书", "type": "file", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "multiple": true, "file_max_count": 5}, "model_key": "payload.certificates", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"theme": {"card": {"content": "<p><br></p>"}, "radius": true}, "collapse": true, "collapse_default_open": true}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1726799513409_1", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "column_attributes_1726799517551_2", "index": {"on": true}, "title": ["专利名称"], "export": {"on": false, "aggre": []}, "import": {"on": false, "aggre": []}, "render": "TableRendersAuto", "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799531755_3", "index": {"on": true}, "title": ["法律状态"], "export": {"on": false, "aggre": [{"_id": "1726820451214_1"}]}, "import": {"on": false, "aggre": [{"_id": "1726820451229_2"}]}, "render": "ComLawStatus", "filters": [], "dataIndex": "", "filteredValue": [], "sortDirections": [], "filteredDetails": [{"_id": "1726820451197_0"}], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799554352_4", "index": {"on": true}, "title": ["转化状态"], "export": {"on": false, "aggre": [{"_id": "1728804651609_1"}]}, "import": {"on": false, "aggre": [{"_id": "1728804651620_2"}]}, "render": "ComConvertStatus", "filters": [], "dataIndex": "", "filteredValue": [], "sortDirections": [], "filteredDetails": [{"_id": "1728804651595_0"}], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799572013_5", "index": {"on": true}, "title": ["科室"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "department.name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799592907_6", "index": {"on": true}, "title": ["转化次数"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "transforms_count", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799605053_7", "index": {"on": true}, "title": ["最近转化更新时间"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "transform.updated_at", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799625598_8", "index": {"on": true}, "title": ["类型"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "patentType", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799636368_9", "index": {"on": true}, "title": ["所属院校"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.belong_school", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799670391_10", "index": {"on": true}, "title": ["缴费情况"], "export": {"on": false, "aggre": []}, "import": {"on": false, "aggre": []}, "render": "ComFeeDetail", "filters": [], "dataIndex": "", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1726799678526_11", "index": {"on": true}, "title": ["是否有课题"], "export": {"on": false}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.has_topic", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}
{"key": "container_layout_1747191879050_58", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-13", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "date_1747191886555_59", "name": "创作完成日期", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的创作完成日期", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "completion_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747191890943_60", "name": "发表状态", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的发表状态", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191992191_0", "label": "已发表", "value": "已发表"}, {"_id": "1747191999532_1", "label": "未发表", "value": "未发表"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747191974819_11"}], "display_configurable_form": {}}, "model_key": "pub_state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747191897494_61", "name": "首次公映/发表日期", "type": "date", "model": {"attr_type": "date"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "pub_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747191903982_62", "name": "创作性质", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的创作性质", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747192065626_0", "label": "原创", "value": "原创"}, {"_id": "1747192065626_1", "label": "改编", "value": "改编"}, {"_id": "1747192065626_2", "label": "翻译", "value": "翻译"}, {"_id": "1747192065626_3", "label": "汇编", "value": "汇编"}, {"_id": "1747192065626_4", "label": "注释", "value": "注释"}, {"_id": "1747192065626_5", "label": "整理", "value": "整理"}, {"_id": "1747192065626_6", "label": "其它", "value": "其它"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747192035169_12"}], "display_configurable_form": {}}, "model_key": "payload.creative_nature", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "textarea_1747191910291_63", "name": "创作性质说明", "type": "textarea", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.creative_nature_desc", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "region_1747191920289_64", "name": "创作完成地点", "type": "region", "model": {"attr_type": "object"}, "rules": [{"type": "any", "message": "请填写正确的创作完成地点", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "payload.completion_location", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747191931060_65", "name": "留存作品样本", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的留存作品样本", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "payload.retain_sample", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "file_1747191938921_66", "name": "作品样本", "type": "file", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "multiple": true}, "model_key": "payload.samples", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"theme": {"card": {"content": "<p><br></p>"}}}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747191879050_58", "conditions": [], "index_attributes": [], "column_attributes": [], "model_key_configuration": []}
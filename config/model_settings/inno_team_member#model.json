{"key": "container_layout_1747113393503_4", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-3", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "api_single_1747113497975_6", "name": "部门", "type": "api_single", "model": {"attr_type": "number"}, "rules": [{"type": "any", "message": "请填写正确的部门", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"path": "/inno/user/departments", "span": 24, "attrs": ["name"], "display": "tag", "table_items": [{"name": "ID", "type": "number", "search": false, "data_index": "id"}, {"name": "部门名称", "type": "string", "search": true, "data_index": "name"}], "import_export_headers": [{"_id": "1747113499412_0"}], "display_configurable_form": {}}, "model_key": "department_id", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "api_single_1747113501313_7", "name": "人员", "type": "api_single", "model": {"attr_type": "number"}, "rules": [{"type": "any", "message": "请填写正确的人员", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"path": "/res/user/users", "span": 24, "attrs": ["name"], "display": "tag", "ransack": "{\"departments_id_eq\":${department_id}}", "table_items": [{"name": "ID", "type": "number", "search": false, "data_index": "id"}, {"name": "姓名", "type": "string", "search": true, "data_index": "name"}], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "user_id", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747113507562_8", "name": "团队角色", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的团队角色", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24, "select": [{"_id": "1747113563937_0", "label": "负责人", "value": "负责人"}, {"_id": "1747113564722_1", "label": "骨干", "value": "骨干"}], "multiple": false, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "role", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747113393503_4", "conditions": [], "index_attributes": [], "column_attributes": [], "model_key_configuration": []}
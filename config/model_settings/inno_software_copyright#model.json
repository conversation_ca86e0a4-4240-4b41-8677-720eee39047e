{"key": "container_layout_1747186028678_1", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-1", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1747186054876_4", "name": "软件全称", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的软件全称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186053291_3", "name": "软件简称", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "short_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186048473_2", "name": "版本号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的版本号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "version_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747186057549_5", "name": "授权日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的授权日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "grant_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747186064922_6", "name": "办理身份", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的办理身份", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747186334669_0", "label": "著作权人", "value": "著作权人"}, {"_id": "1747186343164_1", "label": "代理人", "value": "代理人"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186190910_0"}], "display_configurable_form": {}}, "model_key": "apply_identity", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186082341_9", "name": "著作权人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的著作权人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 16}, "model_key": "owner", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186077325_7", "name": "团队成员", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "member_names", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186086231_11", "name": "证书号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的证书号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "cert_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186084408_10", "name": "登记号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的登记号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "reg_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186080323_8", "name": "流水号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的流水号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "serial_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747186098780_13", "name": "权利取得方式", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的权利取得方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 16, "select": [{"_id": "1747186451793_0", "label": "原始取得", "value": "原始取得"}, {"_id": "1747186460752_1", "label": "继受取得（受让,承受,继承）", "value": "继受取得（受让,承受,继承）"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186206820_1"}], "display_configurable_form": {}}, "model_key": "right_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747186106427_14", "name": "申请日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的申请日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747186095541_12", "name": "权利范围", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的权利范围", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 16, "select": [{"_id": "1747186482312_2", "label": "全部权利", "value": "全部权利"}, {"_id": "1747186483490_3", "label": "部分权利", "value": "部分权利"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186811864_6"}], "display_configurable_form": {}}, "model_key": "right_range", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186110011_15", "name": "申请方式", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的申请方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747186115158_16", "name": "软件分类", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的下拉选择", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747186556994_0", "label": "应用软件", "value": "应用软件"}, {"_id": "1747186559326_1", "label": "嵌入式软件", "value": "嵌入式软件"}, {"_id": "1747186580245_2", "label": "中间件", "value": "中间件"}, {"_id": "1747186582144_3", "label": "操作系统", "value": "操作系统"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186217295_2"}], "display_configurable_form": {}}, "model_key": "soft_kind", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747186125180_17", "name": "开发完成日期", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的开发完成日期", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "complete_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747186129946_18", "name": "发表状态", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的发表状态", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747186627934_0", "label": "已发表", "value": "已发表"}, {"_id": "1747186630258_1", "label": "未发表", "value": "未发表"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186220217_3"}], "display_configurable_form": {}}, "model_key": "pub_state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186145963_19", "name": "联系人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747186148065_20", "name": "联系电话", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系电话", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_mobile", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747186159733_21", "name": "转化意向", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化意向", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747186689197_0", "label": "有意向", "value": "有意向"}, {"_id": "1747186691387_1", "label": "无意向", "value": "无意向"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186225162_4"}], "display_configurable_form": {}}, "model_key": "convert_intention", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747186163552_22", "name": "转化方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747186713722_2", "label": "转让", "value": "转让"}, {"_id": "1747186714549_3", "label": "许可", "value": "许可"}, {"_id": "1747186736109_4", "label": "作价", "value": "作价"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747186768877_5"}], "display_configurable_form": {}}, "model_key": "convert_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "file_1747186169513_23", "name": "软件著作登记证书", "type": "file", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "multiple": true, "file_max_count": 5}, "model_key": "payload.certificates", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"theme": {"card": {"content": "<p><br></p>"}}}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747186028678_1", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "column_attributes_1747206798924_0", "index": {"on": true}, "title": ["授权日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "grant_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206806878_1", "index": {"on": true}, "title": ["软件名称"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747373939858_2", "index": {"on": false}, "title": ["软件简称"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "short_name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747373967720_3", "index": {"on": false}, "title": ["版本号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "version_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747374001337_4", "index": {"on": false}, "title": ["办理身份"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_identity", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206820004_2", "index": {"on": true}, "title": ["著作权人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "owner", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747374688352_5", "index": {"on": false}, "title": ["团队成员"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "member_names", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206838747_3", "index": {"on": true}, "title": ["证书号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "cert_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206847255_4", "index": {"on": true}, "title": ["登记号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "reg_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206862654_5", "index": {"on": true}, "title": ["流水号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "serial_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747374723026_6", "index": {"on": false}, "title": ["权利取得方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "right_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206882126_6", "index": {"on": true}, "title": ["联系人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206896849_7", "index": {"on": true}, "title": ["权利范围"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.own_rights", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206918468_8", "index": {"on": true}, "title": ["申请日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747206926742_9", "index": {"on": true}, "title": ["申请方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376643629_7", "index": {"on": false}, "title": ["软件分类"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "soft_kind", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376652760_8", "index": {"on": false}, "title": ["开发完成日期"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "complete_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376676830_9", "index": {"on": false}, "title": ["发表状态"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "pub_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376698821_10", "index": {"on": false}, "title": ["联系电话"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_mobile", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376716806_11", "index": {"on": false}, "title": ["转化意向"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_intention", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376733874_12", "index": {"on": false}, "title": ["转化方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747376751767_13", "index": {"on": false}, "title": ["转化进度"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "transform_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}
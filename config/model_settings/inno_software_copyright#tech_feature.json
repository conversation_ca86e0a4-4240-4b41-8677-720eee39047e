{"key": "container_layout_1747187256545_25", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-6", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1747187276195_33", "name": "开发的硬件环境", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.dev_hardware", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187274998_32", "name": "运行的硬件环境", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.operating_hardware", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187273895_31", "name": "开发该软件的操作系统", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.dev_system", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187272831_30", "name": "软件开发环境/开发工具", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.sde", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187271765_29", "name": "软件运行平台/操作系统", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.run_system", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187270579_28", "name": "编程语言", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.program_language", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747188387948_36", "name": "源程序量", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.source_code", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187260312_27", "name": "开发目的", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.dev_purpose", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747187258866_26", "name": "面向行业/领域", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.target_field", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "textarea_1747187280250_34", "name": "软件的主要功能", "type": "textarea", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.main_func", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "textarea_1747187282199_35", "name": "软件的技术特点", "type": "textarea", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "payload.tech_feature", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"theme": {"card": {"content": "<p><br></p>"}}}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747187256545_25", "conditions": [], "index_attributes": [], "column_attributes": [], "model_key_configuration": []}
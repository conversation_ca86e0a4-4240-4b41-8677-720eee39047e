{"key": "container_layout_1747212161831_11", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-2", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1747212163063_12", "name": "作品名称", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的作品名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747212170164_13", "name": "知识产权类型", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的知识产权类型", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747212286311_0", "label": "商标", "value": "商标"}, {"_id": "1747212286311_1", "label": "地理标志", "value": "地理标志"}, {"_id": "1747212286311_2", "label": "商业秘密", "value": "商业秘密"}, {"_id": "1747212286311_3", "label": "集成电路布图设计", "value": "集成电路布图设计"}, {"_id": "1747212286311_4", "label": "植物新品种", "value": "植物新品种"}, {"_id": "1747212286311_5", "label": "数据知识产权", "value": "数据知识产权"}, {"_id": "1747212286311_6", "label": "法律规定的其它客体", "value": "法律规定的其它客体"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747212239987_0"}], "display_configurable_form": {}}, "model_key": "kind", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747212177850_15", "name": "授权日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的授权日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "grant_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747212174995_14", "name": "申请日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的申请日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212196843_19", "name": "申请号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的申请号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212200929_21", "name": "证书号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的证书号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "cert_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212195140_18", "name": "权利人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的权利人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"size": "regular", "span": 24}, "model_key": "obligee", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212199213_20", "name": "发明人/设计人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的发明人/设计人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "inventor", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212192062_17", "name": "代理机构", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "agency", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212188829_16", "name": "联系人", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747212453144_25", "name": "联系电话", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系电话", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_mobile", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747212210586_22", "name": "转化意向", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化意向", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747212557853_4", "label": "有意向", "value": "有意向"}, {"_id": "1747212557853_5", "label": "无意向", "value": "无意向"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747212442117_1"}], "display_configurable_form": {}}, "model_key": "convert_intention", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747212212527_23", "name": "转化方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747212518499_0", "label": "转让", "value": "转让"}, {"_id": "1747212518499_1", "label": "许可", "value": "许可"}, {"_id": "1747212518499_2", "label": "作价", "value": "作价"}], "multiple": false, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "convert_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "file_1747212219452_24", "name": "相关证书", "type": "file", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "multiple": true, "file_max_count": 5}, "model_key": "payload.certificates", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747212161831_11", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "column_attributes_1747212601478_27", "index": {"on": true}, "title": ["授权日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "grant_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212607972_28", "index": {"on": true}, "title": ["知识产权类型"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "kind", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212619077_29", "index": {"on": true}, "title": ["知识产权名称"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212631202_30", "index": {"on": true}, "title": ["权利人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "obligee", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212639675_31", "index": {"on": true}, "title": ["发明人/设计人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "inventor", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212650459_32", "index": {"on": true}, "title": ["申请日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212662369_33", "index": {"on": true}, "title": ["申请号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212674104_34", "index": {"on": true}, "title": ["证书号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "cert_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212690647_35", "index": {"on": true}, "title": ["联系人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747212702134_36", "index": {"on": true}, "title": ["代理机构"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "agency", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747286706124_0", "index": {"on": false}, "title": ["联系电话"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_mobile", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747286717428_1", "index": {"on": false}, "title": ["转化意向"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_intention", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747286738340_2", "index": {"on": false}, "title": ["转化方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747286748195_3", "index": {"on": false}, "title": ["转化进度"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "transform_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}
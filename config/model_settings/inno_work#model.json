{"key": "container_layout_1747190767978_38", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-9", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1747190775108_39", "name": "作品名称", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的作品名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747190794391_40", "name": "作品类别", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的作品类别", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191117088_25", "label": "文字作品", "value": "文字作品"}, {"_id": "1747191117088_26", "label": "口述作品", "value": "口述作品"}, {"_id": "1747191117088_27", "label": "音乐作品", "value": "音乐作品"}, {"_id": "1747191117088_28", "label": "戏剧作品", "value": "戏剧作品"}, {"_id": "1747191117088_29", "label": "曲艺作品", "value": "曲艺作品"}, {"_id": "1747191117088_30", "label": "舞蹈作品", "value": "舞蹈作品"}, {"_id": "1747191117088_31", "label": "杂技艺术", "value": "杂技艺术"}, {"_id": "1747191117088_32", "label": "美术作品", "value": "美术作品"}, {"_id": "1747191117088_33", "label": "建筑作品", "value": "建筑作品"}, {"_id": "1747191117088_34", "label": "摄影作品", "value": "摄影作品"}, {"_id": "1747191117088_35", "label": "电影作品", "value": "电影作品"}, {"_id": "1747191117088_36", "label": "以类似摄影电影方法创作作品", "value": "以类似摄影电影方法创作作品"}, {"_id": "1747191117088_37", "label": "工程设计图/产品设计图", "value": "工程设计图/产品设计图"}, {"_id": "1747191117088_38", "label": "地图/示意图", "value": "地图/示意图"}, {"_id": "1747191117088_39", "label": "模型作品", "value": "模型作品"}, {"_id": "1747191117088_40", "label": "其它作品", "value": "其它作品"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747190796667_7"}], "display_configurable_form": {}}, "model_key": "category", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747190804655_41", "name": "登记日期", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的登记日期", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "reg_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190815069_43", "name": "著作权人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的著作权人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "owner", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190822959_45", "name": "制片人/作者", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的制片人/作者", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "marker", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190819723_44", "name": "登记号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的登记号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "reg_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190809723_42", "name": "流水号", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的流水号", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "serial_no", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "date_1747190833195_46", "name": "申请日", "type": "date", "model": {"attr_type": "date"}, "rules": [{"type": "any", "message": "请填写正确的申请日", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "apply_date", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747190841259_47", "name": "办理身份", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的办理身份", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191340420_0", "label": "著作权人", "value": "著作权人"}, {"_id": "1747191342103_1", "label": "代理人", "value": "代理人"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747191314986_8"}], "display_configurable_form": {}}, "model_key": "apply_identity", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190848324_48", "name": "代理机构", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "agency", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747190856608_49", "name": "权利取得方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的权利取得方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191422367_2", "label": "原始", "value": "原始"}, {"_id": "1747191422367_3", "label": "继承", "value": "继承"}, {"_id": "1747191422367_4", "label": "承受", "value": "承受"}, {"_id": "1747191422367_5", "label": "转让", "value": "转让"}, {"_id": "1747191422367_6", "label": "其他", "value": "其他"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747191364568_9"}], "display_configurable_form": {}}, "model_key": "obtain_rights_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747190861625_50", "name": "权利归属方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的权利归属方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191479730_8", "label": "个人作品", "value": "个人作品"}, {"_id": "1747191479730_9", "label": "合作作品", "value": "合作作品"}, {"_id": "1747191479730_10", "label": "法人作品", "value": "法人作品"}, {"_id": "1747191479730_11", "label": "职务作品", "value": "职务作品"}, {"_id": "1747191479730_12", "label": "委托作品", "value": "委托作品"}], "multiple": false, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "rights_belong_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "checkbox_1747190870180_51", "name": "权利拥有状况", "type": "checkbox", "model": {"attr_type": "string"}, "rules": [{"type": "array", "message": "请填写正确的权利拥有状况", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24, "select": [{"_id": "1747191609086_13", "label": "发表权", "value": "发表权"}, {"_id": "1747191609086_14", "label": "署名权", "value": "署名权"}, {"_id": "1747191609086_15", "label": "修改权", "value": "修改权"}, {"_id": "1747191609086_16", "label": "保护作品完整权", "value": "保护作品完整权"}, {"_id": "1747191609086_17", "label": "复制权", "value": "复制权"}, {"_id": "1747191609086_18", "label": "发行权", "value": "发行权"}, {"_id": "1747191609086_19", "label": "出租权", "value": "出租权"}, {"_id": "1747191609086_20", "label": "展览权", "value": "展览权"}, {"_id": "1747191609086_21", "label": "放映权", "value": "放映权"}, {"_id": "1747191609086_22", "label": "广播权", "value": "广播权"}, {"_id": "1747191609086_23", "label": "信息网络传播权", "value": "信息网络传播权"}, {"_id": "1747191609086_24", "label": "摄制权", "value": "摄制权"}, {"_id": "1747191609086_25", "label": "改编权", "value": "改编权"}, {"_id": "1747191609086_26", "label": "翻译权", "value": "翻译权"}, {"_id": "1747191609086_27", "label": "汇编权", "value": "汇编权"}, {"_id": "1747191609086_28", "label": "其它", "value": "其它"}], "multiple": true, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "payload.own_rights", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190881042_53", "name": "联系人", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系人", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1747190878240_52", "name": "联系电话", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的联系电话", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8}, "model_key": "contact_mobile", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1747190891365_55", "name": "转化意向", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化意向", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191728354_0", "label": "有意向", "value": "有意向"}, {"_id": "1747191735944_1", "label": "无意向", "value": "无意向"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1747191714564_10"}], "display_configurable_form": {}}, "model_key": "convert_intention", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1747190884006_54", "name": "转化方式", "type": "select", "model": {"attr_type": "string"}, "rules": [{"type": "any", "message": "请填写正确的转化方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 8, "select": [{"_id": "1747191781845_3", "label": "转让", "value": "转让"}, {"_id": "1747191781845_4", "label": "许可", "value": "许可"}, {"_id": "1747191781845_5", "label": "作价", "value": "作价"}], "multiple": false, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "convert_way", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "file_1747190899894_56", "name": "作品登记证书", "type": "file", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "multiple": true, "file_max_count": 5}, "model_key": "payload.certificates", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {}, "form": {}, "background": {}}, "disabled_actions": {}}, "model_key": "container_layout_1747190767978_38", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "column_attributes_1747207892730_0", "index": {"on": true}, "title": ["登记日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "reg_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747207893892_1", "index": {"on": true}, "title": ["作品名称"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747207989051_2", "index": {"on": true}, "title": ["著作权人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "owner", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208003496_3", "index": {"on": true}, "title": ["制片人/作者"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "maker", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208020254_4", "index": {"on": true}, "title": ["作品类别"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "category", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208037806_5", "index": {"on": true}, "title": ["登记号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "reg_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208049039_6", "index": {"on": true}, "title": ["流水号"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "serial_no", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208059551_7", "index": {"on": true}, "title": ["申请日"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208073570_8", "index": {"on": true}, "title": ["联系人"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747208086488_9", "index": {"on": true}, "title": ["代理机构"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "agency", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380406423_14", "index": {"on": false}, "title": ["办理身份"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "apply_identity", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380427451_15", "index": {"on": false}, "title": ["权利取得方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "obtain_rights_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380449903_16", "index": {"on": false}, "title": ["权利归属方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "rights_belong_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380499893_17", "index": {"on": false}, "title": ["权利拥有情况"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.own_rights", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380522938_18", "index": {"on": false}, "title": ["联系电话"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "contact_mobile", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380544011_19", "index": {"on": false}, "title": ["创作完成日期"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "completion_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380566453_20", "index": {"on": false}, "title": ["发表状态"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "pub_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380583355_21", "index": {"on": false}, "title": ["首次公映/发表日期"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "pub_date", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380611446_22", "index": {"on": false}, "title": ["创作性质"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.creative_nature", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380643054_23", "index": {"on": false}, "title": ["创作性质说明"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.creative_nature_desc", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380677087_24", "index": {"on": false}, "title": ["创作完成地点"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "completion_location", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380708058_25", "index": {"on": false}, "title": ["留存作品样本"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "payload.retain_sample", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380726379_26", "index": {"on": false}, "title": ["转化意向"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_intention", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380736250_27", "index": {"on": false}, "title": ["转化方式"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "convert_way", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1747380751629_28", "index": {"on": false}, "title": ["转化进度"], "export": {"on": true}, "import": {"on": false}, "render": "TableRendersAuto", "filters": [], "dataIndex": "transform_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}
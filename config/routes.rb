Rails.application.routes.draw do
  namespace :inno do
    namespace :user do

      resources :users, only: [:index]

      resources :patents do
        get :statistic, on: :collection
        post :distribute, on: :member

        markable
        diskable
        versionable

        exportable

        resources :ownerships, only: [:index, :show]
        resources :payments
      end

      resources :projects do
        post :statistic, on: :collection
      end

      resources :project_tags

      resources :settings
      resources :funds do
        exportable
        post :statistic, on: :collection
      end

      resources :transforms, only: [:index, :show, :create] do
        get :statistic, on: :collection

        eventable

        resources :transform_items, only: [:index, :create, :show]
      end

      resources :papers
      resources :awards
      resources :scientific_researches
      resources :competitive_products
      resources :team_members
      resources :departments, only: [:index]

      resources :works do
        get :statistic, on: :collection
        post :distribute, on: :member

        markable
        diskable
        versionable
        exportable

        resources :ownerships, only: [:index, :show, :create]
      end
      resources :software_copyrights do
        get :statistic, on: :collection
        post :distribute, on: :member

        markable
        diskable
        versionable
        exportable

        resources :ownerships, only: [:index, :show]
      end

      resources :other_ips do
        post :distribute, on: :member

        markable
        diskable
        versionable
        exportable

        resources :ownerships, only: [:index, :show]
      end
    end
  end
end

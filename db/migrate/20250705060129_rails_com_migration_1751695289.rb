class RailsComMigration1751695289 < ActiveRecord::Migration[7.1]

  def change
    add_column :inno_competitive_products, :model_flag, :string, comment: "model flag，对应model_setting的flag"
    add_column :inno_competitive_products, :model_payload, :jsonb, comment: "model payload存储的字段"
    add_column :inno_competitive_products, :model_payload_summary, :jsonb, comment: "model summary存储的字段"
    add_column :inno_competitive_products, :model_detail, :jsonb, comment: "model 存储的详情字段"
  end
end

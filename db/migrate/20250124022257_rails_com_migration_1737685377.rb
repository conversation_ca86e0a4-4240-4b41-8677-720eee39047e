class RailsComMigration1737685377 < ActiveRecord::Migration[7.1]

  def change
    add_column :inno_patents, :ip_state, :string
    create_table :inno_software_copyrights do |t|
      t.references :app
      t.string :name, comment: "软件名称"
      t.string :serial_no, comment: "流水号"
      t.string :reg_no, comment: "登记号"
      t.string :owner, comment: "著作权人"
      t.string :right_way, comment: "权利取得方式"
      t.string :right_range, comment: "权利防伪"
      t.string :cert_no, comment: "证书号"
      t.date :cert_date, comment: "发证时间"
      t.string :ip_state
      t.string :transform_state, comment: "转化状态"
      t.timestamps
    end
    create_table :inno_works do |t|
      t.references :app
      t.string :name, comment: "作品名称"
      t.string :serial_no, comment: "流水号"
      t.string :category, comment: "作品类别"
      t.string :maker, comment: "制片者"
      t.string :owner, comment: "著作权人"
      t.date :completion_date, comment: "创作完成日期"
      t.date :pub_date, comment: "首次公映日期"
      t.string :agency, comment: "代理机构"
      t.date :reg_date, comment: "登记日期"
      t.string :reg_no, comment: "登记号"
      t.string :ip_state
      t.string :transform_state, comment: "转化状态"
      t.timestamps
    end
  end
end

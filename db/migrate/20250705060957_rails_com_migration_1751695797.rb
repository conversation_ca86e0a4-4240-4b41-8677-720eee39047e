class RailsComMigration1751695797 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_payments do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.references :patent
      t.references :user
      t.string :type, comment: "STI属性"
      t.string :seq, comment: "编号"
      t.date :start_date, comment: "生效时间"
      t.date :end_date, comment: "失效时间"
      t.string :name, comment: "名称"
      t.string :state
      t.decimal :amount, comment: "金额"
      t.datetime :paid_at, comment: "支付时间"
      t.string :paid_type, comment: "支付方式"
      t.jsonb :attachment, comment: "附件"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    add_column :inno_patents, :payment_start_date, :date, comment: "生效时间"
    add_column :inno_patents, :payment_end_date, :date, comment: "失效时间"
    add_column :inno_patents, :payment, :boolean, comment: "专利是否需要付费使用"
  end
end

class RailsComMigration1750516778 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_funds do |t|
      t.references :app
      t.references :project
      t.references :creator
      t.string :seq, comment: "编号"
      t.datetime :planned_at
      t.boolean :is_planned
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.decimal :amount, comment: "金额"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.jsonb :attachment, comment: "附件"
      t.decimal :received_amount, comment: "到账金额"
      t.date :received_date, comment: "收款日期"
      t.decimal :cost_amount, comment: "成本"
      t.decimal :income_amount, comment: "收入"
      t.decimal :profit_amount, comment: "收益"
      t.decimal :tech_operator_amount, comment: "运营金额"
      t.decimal :tech_talent_amount, comment: "人才培养金额"
      t.decimal :group_assign_amount, comment: "分配金额"
      t.string :paid_unit, comment: "支付单位"
      t.string :paid_account, comment: "支付账户"
      t.string :paid_bank, comment: "支付银行"
      t.date :paid_date, comment: "支付日期"
      t.string :paid_num, comment: "支付流水号"
      t.string :paid_ticket_date, comment: "开票时间"
      t.timestamps
    end
  end
end

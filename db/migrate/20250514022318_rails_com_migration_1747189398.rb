class RailsComMigration1747189398 < ActiveRecord::Migration[7.1]

  def change
    add_reference :inno_software_copyrights, :creator
    add_column :inno_software_copyrights, :short_name, :string, comment: "软件简称"
    add_column :inno_software_copyrights, :version_no, :string, comment: "版本号"
    add_column :inno_software_copyrights, :grant_date, :date, comment: "授权日"
    add_column :inno_software_copyrights, :apply_identity, :string, comment: "办理身份"
    add_column :inno_software_copyrights, :member_names, :string, comment: "团队成员"
    add_column :inno_software_copyrights, :apply_date, :date, comment: "申请日"
    add_column :inno_software_copyrights, :apply_way, :string, comment: "申请方式"
    add_column :inno_software_copyrights, :soft_kind, :string, comment: "软件分类"
    add_column :inno_software_copyrights, :complete_date, :date, comment: "开发完成日期"
    add_column :inno_software_copyrights, :pub_state, :string, comment: "发表状态"
    add_column :inno_software_copyrights, :contact_name, :string, comment: "联系人"
    add_column :inno_software_copyrights, :contact_mobile, :string, comment: "联系方式"
    add_column :inno_software_copyrights, :convert_intention, :string, comment: "转化意向"
    add_column :inno_software_copyrights, :convert_way, :string, comment: "转化方式"
    add_column :inno_software_copyrights, :payload, :jsonb
    remove_column :inno_software_copyrights, :cert_date, :date, comment: "发证时间"
  end
end

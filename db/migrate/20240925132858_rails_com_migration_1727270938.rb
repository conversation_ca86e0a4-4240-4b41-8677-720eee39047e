class RailsComMigration1727270938 < ActiveRecord::Migration[7.1]

  def change
    create_table :favor_mark_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "favor_mark_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "favor_mark_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "favor_mark_actions_uk_action_target_user"
    end
    create_table :favor_folders do |t|
      t.references :app
      t.references :user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "收藏夹名称"
      t.jsonb :cover_image, comment: "封面图"
      t.jsonb :content, comment: "详情"
      t.integer :position, comment: "排序"
      t.jsonb :option, comment: "额外配置信息"
      t.timestamps
    end
  end
end

class RailsComMigration1747125306 < ActiveRecord::Migration[7.1]

  def change
    add_column :inno_patents, :patentee, :string, comment: "专利权人"
    add_column :inno_patents, :inventors, :string, comment: "发明人"
    add_column :inno_patents, :contact_name, :string, comment: "联系人"
    add_column :inno_patents, :contact_mobile, :string, comment: "联系电话"
    add_column :inno_patents, :agency, :string, comment: "代理机构"
    add_column :inno_patents, :agency_code, :string, comment: "代理机构代码"
    add_column :inno_patents, :patentee_state, :string, comment: "专利权人状态"
    add_column :inno_patents, :state, :string, comment: "专利权状态"
    add_column :inno_patents, :convert_intention, :string, comment: "转化意向"
    add_column :inno_patents, :convert_way, :string, comment: "转化意向"
    remove_column :inno_patents, :pub_no, :string, comment: "公开/公告号"
    remove_column :inno_patents, :applicant, :string, comment: "申请人"
    remove_column :inno_patents, :investors, :string, comment: "发明人"
  end
end

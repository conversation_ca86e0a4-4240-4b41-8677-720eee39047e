class RailsComMigration1726119730 < ActiveRecord::Migration[7.1]

  def change
    create_table :active_storage_blobs do |t|
      t.jsonb :metadata, comment: "额外信息"
      t.string :app_code, comment: "app标识"
      t.string :key, comment: "key"
      t.string :filename, comment: "文件名称"
      t.string :content_type, comment: "文件类型"
      t.string :service_name, comment: "服务名称"
      t.integer :byte_size, comment: "文件大小"
      t.string :checksum, comment: "校验位"
      t.timestamps
      t.index [:key], unique: true
    end
    create_table :active_storage_attachments do |t|
      t.references :record, polymorphic: true
      t.references :blob
      t.string :name
      t.timestamps
      t.index [:record_type, :record_id, :name, :blob_id], unique: true
    end
    create_table :inno_transforms do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.string :state, comment: "状态属性"
      t.timestamps
    end
    create_table :inno_patents do |t|
      t.references :app
      t.string :name, comment: "专利名称"
      t.string :apply_no, comment: "专利申请号"
      t.date :apply_date, comment: "申请日期"
      t.string :pub_no, comment: "公开/公告号"
      t.date :pub_date, comment: "公开/公告日期"
      t.text :abstract, comment: "专利摘要"
      t.string :applicant, comment: "申请人"
      t.string :investors, comment: "发明人"
      t.string :ipc, comment: "IPC分类号"
      t.string :patent_type, comment: "专利类型"
      t.string :legal_state, comment: "法律状态"
      t.string :transform_state, comment: "转化状态"
      t.jsonb :payload, comment: "额外信息"
      t.timestamps
    end
    create_table :version_relationships do |t|
      t.references :app
      t.references :resource, polymorphic: true
      t.references :real_resource, polymorphic: true
      t.references :version, polymorphic: true
      t.references :operator, polymorphic: true
      t.timestamps
    end
    create_table :users_roles do |t|
      t.references :user
      t.references :role
      t.timestamps
    end
    create_table :com_record_storages do |t|
      t.references :user
      t.string :key, comment: "缓存区key"
      t.jsonb :storage, comment: "属性暂存区"
      t.timestamps
    end
    create_table :async_tasks do |t|
      t.references :app
      t.references :user
      t.references :taskable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :type, comment: "STI属性"
      t.string :flag, comment: "程序使用参数，唯一标识，前端配合使用"
      t.string :name, comment: "任务名称"
      t.integer :progress, comment: "进度(取整数)"
      t.string :state
      t.string :perform_args, comment: "执行参数"
      t.jsonb :options, comment: "启动执行参数"
      t.jsonb :payload, comment: "处理信息"
      t.jsonb :result, comment: "异步处理的结果信息"
      t.jsonb :meta, comment: "额外信息"
      t.timestamps
    end
    create_table :com_themes do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "主题名称"
      t.jsonb :conf, comment: "主题配置"
      t.timestamps
    end
    create_table :com_search_items do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "搜索条件 "
      t.integer :position, comment: "位置"
      t.string :group_name, comment: "分组标识"
      t.boolean :enabled, comment: "是否启用"
      t.jsonb :conditions, comment: "具体ransack搜索条件"
      t.timestamps
    end
    create_table :paper_trail_versions do |t|
      t.references :operator, polymorphic: true
      t.string :item_type
      t.integer :item_id
      t.string :event, comment: "create, update, destroy"
      t.string :whodunnit, comment: "whodunnit"
      t.jsonb :object, comment: "object attributes"
      t.jsonb :object_changes, comment: "object changes"
      t.jsonb :controller_info, comment: "controller info"
      t.jsonb :model_info, comment: "model info"
      t.timestamps
      t.index [:item_type, :item_id], name: "index_versions_on_item_id_item_type"
    end
    create_table :page_settings do |t|
      t.references :app
      t.string :seq, comment: "编号"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "页面配置名称"
      t.jsonb :conf, comment: "页面配置的json结构"
      t.timestamps
    end
    create_table :model_settings do |t|
      t.references :model_define
      t.references :setable, polymorphic: true
      t.references :app
      t.references :forms_template
      t.string :flag, comment: "同一个模型中的不同定义，其中model代表是这个对象的模型"
      t.string :flag_name, comment: "flag对应中文名称"
      t.jsonb :form, comment: "可以直接定义表单"
      t.jsonb :form_setting, comment: "表单结构"
      t.jsonb :api_config, comment: "API Config"
      t.timestamps
    end
    create_table :model_defines do |t|
      t.string :klass, comment: "对应设置的Model名称"
      t.string :name, comment: "模型设置的中文名"
      t.string :association_chain
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.timestamps
    end
    create_table :model_confs do |t|
      t.references :model_define
      t.string :name, comment: "名称"
      t.string :klass, comment: "类名"
      t.jsonb :conf, comment: "具体配置"
      t.timestamps
    end
    create_table :data_forms do |t|
      t.references :app
      t.references :create_user
      t.references :source, polymorphic: true
      t.references :record, polymorphic: true
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :flag, comment: "可用作同一 source 下不同的关联关系的区分"
      t.string :source_flag, comment: "关联source的flag"
      t.string :state, comment: "数据状态"
      t.jsonb :payload, comment: "存储的信息"
      t.jsonb :summary, comment: "通过form生成的缩略信息"
      t.jsonb :form_conf, comment: "表单的配置，里面支持多态的方式"
      t.jsonb :options, comment: "额外的数据信息"
      t.jsonb :meta, comment: "预留后续的数据存储"
      t.string :form_conf_seq, comment: "表单配置的seq，方便进行检索"
      t.timestamps
    end
    create_table :component_settings do |t|
      t.references :app
      t.string :seq, comment: "编号"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "组件配置名称"
      t.string :component_klass, comment: "组件类名称"
      t.string :component_path, comment: "组件类路径"
      t.jsonb :conf, comment: "组件配置的json结构"
      t.timestamps
    end
    create_table :api_settings do |t|
      t.references :model_define
      t.references :app
      t.string :klass, comment: "对应的active record class name"
      t.string :action, comment: "对应controller的action"
      t.string :uid, comment: "自动生成的唯一标识"
      t.jsonb :extract_conf, comment: "数据抽取配置"
      t.timestamps
    end
    create_table :com_version_histories do |t|
      t.references :app
      t.references :creator
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "版本发布名称"
      t.string :version, comment: "版本号"
      t.jsonb :content, comment: "发布说明"
      t.integer :position, comment: "发布顺序"
      t.timestamps
    end
    create_table :com_private_policies do |t|
      t.references :app
      t.string :name, comment: "条款名称"
      t.string :key, comment: "关键字，可能有不同业务模块需要使用的关键字"
      t.jsonb :content, comment: "隐私条款内容"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :data_transfers do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.references :target, polymorphic: true
      t.string :op, comment: "操作"
      t.jsonb :infos, comment: "额外信息"
      t.timestamps
    end
    create_table :data_counter_stats do |t|
      t.references :countable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.date :date, comment: "日期，如果是年、月则存第一天"
      t.integer :hour, comment: "小时"
      t.string :period
      t.integer :view_count, comment: "浏览量"
      t.integer :action_count, comment: "使用量"
      t.integer :user_count, comment: "用户量"
      t.timestamps
    end
    create_table :data_view_logs do |t|
      t.references :app
      t.references :user
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :view_at, comment: "最新访问时间"
      t.timestamps
    end
    create_table :actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "actions_uk_action_target_user"
    end
    create_table :tanent_resources do |t|
      t.references :app
      t.references :tanent
      t.references :resource, polymorphic: true
      t.jsonb :payload, comment: "存额外信息"
      t.timestamps
    end
    create_table :org_ownerships do |t|
      t.references :org
      t.references :user
      t.timestamps
    end
    create_table :org_clients do |t|
      t.references :app
      t.references :org
      t.references :client, polymorphic: true
      t.timestamps
    end
    create_table :forms_templates do |t|
      t.references :app
      t.string :uuid, comment: "表单的唯一标识，可以替代id给前端使用"
      t.string :name, comment: "表单的名称"
      t.jsonb :form, comment: "表单配置的内容"
      t.jsonb :form_setting, comment: "表单内容"
      t.timestamps
    end
    create_table :tanent_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "tanent_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "tanent_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "tanent_permit_actions_uk_action_target_user"
    end
    create_table :tanents do |t|
      t.references :app
      t.string :code, comment: "租户标识"
      t.string :name, comment: "租户名称"
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :res_tags_relations do |t|
      t.references :tag
      t.references :user
      t.references :org
      t.references :member
      t.timestamps
    end
    create_table :res_tags do |t|
      t.references :app
      t.references :org
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "标签名称"
      t.string :color, comment: "标签颜色"
      t.timestamps
    end
    create_table :org_requests do |t|
      t.references :app
      t.references :user
      t.references :member_identity
      t.references :member
      t.references :org
      t.references :org_member_identity
      t.references :org_member
      t.references :org_identity
      t.references :tanent
      t.string :create_instance_state, comment: "状态"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "组织名称"
      t.string :code, comment: "组织标识"
      t.jsonb :org_payload, comment: "相关信息，会存储到org的payload里"
      t.jsonb :member_payload, comment: "相关信息，会存储到member的payload里"
      t.string :type, comment: "STI"
      t.string :state, comment: "状态: draft, approving"
      t.datetime :approval_at, comment: "审批通过时间"
      t.jsonb :options, comment: "其他预留信息"
      t.timestamps
    end
    create_table :org_members do |t|
      t.references :app
      t.references :org
      t.references :org_member_identity
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.date :effective_at, comment: "生效时间"
      t.date :invalid_at, comment: "失效时间"
      t.string :type, comment: "STI类型"
      t.timestamps
    end
    create_table :org_member_identities do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :code, comment: "标识"
      t.string :org_member_type, comment: "OrgMember的类型"
      t.jsonb :settle_in_form, comment: "入驻申请表单"
      t.jsonb :postpone_form, comment: "延期申请表单"
      t.jsonb :form, comment: "表单"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :memberships do |t|
      t.references :app
      t.references :org
      t.references :user
      t.references :member
      t.references :department
      t.references :duty
      t.datetime :effective_at, comment: "生效时间，可以为空"
      t.datetime :invalid_at, comment: "失效时间，可以为空"
      t.jsonb :payload
      t.timestamps
    end
    create_table :member_requests do |t|
      t.references :app
      t.references :user
      t.references :member_identity
      t.string :create_instance_state, comment: "状态"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "请求名称"
      t.jsonb :payload, comment: "相关信息，会存储到member的payload里"
      t.jsonb :member_attributes, comment: "相关信息，会存储到member的attributes里"
      t.jsonb :options, comment: "加入什么组织和岗位，相关配置"
      t.string :state, comment: "状态"
      t.timestamps
    end
    create_table :duty_groups do |t|
      t.references :app
      t.references :org
      t.string :name, comment: "角色组名称"
      t.integer :position, comment: "排序"
      t.string :code, comment: "岗位组标识"
      t.timestamps
    end
    create_table :department_identities do |t|
      t.references :app
      t.string :name, comment: "部门类型名称"
      t.string :department_type, comment: "Department的类型，可能会关系到Department的STI"
      t.string :color, comment: "标签颜色"
      t.timestamps
    end
    create_table :res_books do |t|
      t.references :app
      t.references :user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :desc, comment: "说明"
      t.string :relation_type, comment: "通讯录的类型"
      t.timestamps
    end
    create_table :res_book_relations do |t|
      t.references :book
      t.references :source, polymorphic: true
      t.timestamps
    end
    create_table :apps do |t|
      t.string :code, comment: "应用标识"
      t.string :name, comment: "应用的名称"
      t.jsonb :settings, comment: "配置信息"
      t.timestamps
    end
    create_table :tofu_entries do |t|
      t.references :source, polymorphic: true
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :ancestry, comment: "树形结构"
      t.string :platform
      t.string :layout, comment: "点击以后前端使用的layout"
      t.string :type, comment: "STI"
      t.string :name, comment: "名称"
      t.string :desc, comment: "描述"
      t.text :icon, comment: "显示的图片或者图标"
      t.text :url, comment: "跳转地址，如果只是menu，可以为空"
      t.string :open_mode, comment: "打开页面的方式"
      t.integer :position, comment: "位置"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.timestamps
    end
    create_table :role_permission_relations do |t|
      t.references :role
      t.references :permission, polymorphic: true
      t.timestamps
    end
    create_table :route_settings do |t|
      t.references :app
      t.references :mod
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "module名称"
      t.jsonb :conf, comment: "module导出路由"
      t.timestamps
    end
    create_table :permit_permissions do |t|
      t.references :app
      t.references :mod
      t.references :tanent
      t.references :user
      t.references :route_setting
      t.string :platform, comment: "平台"
      t.string :aname, comment: "action名称"
      t.string :cname, comment: "controller名称"
      t.string :klass, comment: "controller"
      t.string :action, comment: "action"
      t.jsonb :whitelist
      t.jsonb :blacklist
      t.jsonb :payload
      t.integer :position
      t.string :key
      t.timestamps
    end
    create_table :mods do |t|
      t.string :name, comment: "模块名称"
      t.string :key, comment: "模块对应查找的key值"
      t.timestamps
    end
    create_table :data_scopes do |t|
      t.references :app
      t.references :tanent
      t.references :user
      t.string :name, comment: "名称"
      t.jsonb :config, comment: "配置"
      t.jsonb :payload
      t.timestamps
    end
    create_table :state_bpm_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_bpm_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_bpm_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "state_bpm_actions_uk_action_target_user"
    end
    create_table :state_activate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_activate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_activate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "state_activate_actions_uk_action_target_user"
    end
    create_table :state_transitions do |t|
      t.references :machine
      t.references :source
      t.references :target
      t.references :terminate_place
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :type, comment: "STI"
      t.string :seq, comment: "transition的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :event_name, comment: "操作的英文名称"
      t.string :flag, comment: "程序使用的标记位"
      t.boolean :auto_trigger, comment: "是否自动触发"
      t.jsonb :options, comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
      t.jsonb :trigger_options, comment: "transition的触发器处理"
      t.timestamps
    end
    create_table :state_tokens do |t|
      t.references :app
      t.references :machine
      t.references :event
      t.references :transition
      t.references :token_define
      t.references :token_source, polymorphic: true
      t.references :eventable, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.string :type, comment: "STI"
      t.string :name, comment: "处理节点名称"
      t.string :flag, comment: "处理节点flag"
      t.string :user_name, comment: "user的名称"
      t.string :state
      t.jsonb :token_source_attributes, comment: "token source的attributes缓存"
      t.timestamps
    end
    create_table :state_token_defines do |t|
      t.references :machine
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "token_define的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :type, comment: "STI"
      t.string :token_type, comment: "对应token的type"
      t.string :token_flag, comment: "对应token的flag"
      t.string :token_default_state, comment: "token生成的默认state"
      t.jsonb :token_form, comment: "token表单"
      t.jsonb :options, comment: "配置信息"
      t.jsonb :limit_options, comment: "限制要求配置信息，包括schedule循环、次数要求等"
      t.jsonb :user_options, comment: "限制可以操作的用户类型设置"
      t.timestamps
    end
    create_table :state_places do |t|
      t.references :machine
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :state, comment: "节点状态"
      t.string :type, comment: "STI"
      t.integer :position, comment: "排序"
      t.jsonb :options, comment: "配置信息"
      t.jsonb :trigger_options, comment: "place的触发器处理"
      t.timestamps
    end
    create_table :department_hierarchies do |t|
      t.integer :ancestor_id
      t.integer :descendant_id
      t.integer :generations
      t.datetime :created_at
      t.datetime :updated_at
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "department_anc_desc_idx"
    end
    create_table :departments do |t|
      t.references :org
      t.references :root_org
      t.references :department_identity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :code, comment: "组织标识"
      t.string :name, comment: "组织名称"
      t.string :short_name, comment: "组织简称"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :org_identities do |t|
      t.references :app
      t.string :name, comment: "组织类型名称"
      t.string :org_type, comment: "Org的类型，可能会关系到Org的STI"
      t.integer :orgs_count, comment: "关联的Org数量"
      t.jsonb :form, comment: "Member配置的表单"
      t.timestamps
    end
    create_table :org_hierarchies do |t|
      t.integer :ancestor_id
      t.integer :descendant_id
      t.integer :generations
      t.datetime :created_at
      t.datetime :updated_at
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "org_anc_desc_idx"
    end
    create_table :orgs do |t|
      t.references :app
      t.references :org_identity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :code, comment: "组织标识"
      t.string :name, comment: "组织名称"
      t.string :short_name, comment: "组织简称"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :role_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "role_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "role_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "role_permit_actions_uk_action_target_user"
    end
    create_table :roles do |t|
      t.references :resource, polymorphic: true
      t.references :mod
      t.string :name, comment: "权限标识"
      t.string :label, comment: "显示名称"
      t.string :pinyin, comment: "拼音,排序用"
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.timestamps
    end
    create_table :duties do |t|
      t.references :duty_group
      t.references :org
      t.references :department
      t.string :name, comment: "职务名称"
      t.string :rank, comment: "职务等级"
      t.integer :position, comment: "排序"
      t.string :code, comment: "岗位标识"
      t.timestamps
    end
    create_table :member_identity_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "member_identity_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "member_identity_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "member_identity_permit_actions_uk_action_target_user"
    end
    create_table :member_identities do |t|
      t.references :app
      t.references :org
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :ancestry, comment: "树形结构"
      t.string :name, comment: "身份名称"
      t.string :member_type, comment: "Member的类型"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.jsonb :form, comment: "Member配置的表单"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :res_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "res_permit_actions_uk_action_target_user"
    end
    create_table :state_machines do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "状态机名称"
      t.string :state_attr_name, comment: "状态机对应模型属性名称"
      t.string :klass, comment: "类名"
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.string :flag, comment: "程序使用标识"
      t.timestamps
    end
    create_table :state_events do |t|
      t.references :app
      t.references :machine
      t.references :transition
      t.references :user, polymorphic: true
      t.references :source
      t.references :target
      t.references :eventable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
    end
    create_table :res_user_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_user_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_user_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "res_user_actions_uk_action_target_user"
    end
    create_table :res_member_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_member_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_member_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "res_member_actions_uk_action_target_user"
    end
    create_table :members do |t|
      t.references :user
      t.references :member_identity
      t.references :app
      t.references :member_request
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
      t.string :code, comment: "用户标识"
      t.datetime :blocked_at
      t.boolean :is_blocked
      t.timestamps
    end
    create_table :users do |t|
      t.references :app
      t.references :tanent
      t.references :ref_user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :account, comment: "账号，关联登录"
      t.string :name, comment: "用户姓名"
      t.string :nickname, comment: "用户昵称"
      t.string :pinyin, comment: "用户名拼音"
      t.string :mobile, comment: "用户手机号"
      t.string :email, comment: "用户邮箱"
      t.string :gender, comment: "性别"
      t.jsonb :avatar, comment: "用户头像"
      t.string :identity_id, comment: "证件号码，需要时候可以作为唯一标识"
      t.datetime :last_visit_at, comment: "最后访问时间"
      t.datetime :blocked_at
      t.boolean :is_blocked
      t.timestamps
    end
  end
end

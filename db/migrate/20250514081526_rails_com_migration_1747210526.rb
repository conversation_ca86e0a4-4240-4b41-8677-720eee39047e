class RailsComMigration1747210526 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_other_ips do |t|
      t.references :creator
      t.references :app
      t.integer :transforms_count, comment: "转化次数"
      t.string :transform_state, comment: "转化状态"
      t.integer :stars_count
      t.string :name, comment: "名称"
      t.string :kind, comment: "类型"
      t.date :grant_date, comment: "授权日"
      t.date :apply_date, comment: "申请日"
      t.string :apply_no, comment: "申请号"
      t.string :cert_no, comment: "证书号"
      t.string :obligee, comment: "权利人"
      t.string :inventor, comment: "发明人/设计人"
      t.string :agency, comment: "代理机构"
      t.string :contact_name, comment: "联系人"
      t.string :contact_mobile, comment: "联系方式"
      t.string :convert_intention, comment: "转化意向"
      t.string :convert_way, comment: "转化方式"
      t.jsonb :payload
      t.timestamps
    end
  end
end

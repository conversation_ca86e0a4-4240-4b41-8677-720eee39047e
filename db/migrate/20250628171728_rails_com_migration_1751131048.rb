class RailsComMigration1751131048 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_settings do |t|
      t.references :app
      t.string :name, comment: "名称"
      t.string :state
      t.integer :position, comment: "排序"
      t.jsonb :option, comment: "配置"
      t.timestamps
    end
    add_column :inno_funds, :group_user_count, :integer, comment: "项目组分配人数"
    add_column :inno_funds, :meta, :jsonb, comment: "配置字段"
  end
end

class RailsComMigration1747190279 < ActiveRecord::Migration[7.1]

  def change
    add_reference :inno_works, :creator
    add_column :inno_works, :apply_date, :date, comment: "申请日期"
    add_column :inno_works, :apply_identity, :string, comment: "办理身份"
    add_column :inno_works, :obtain_rights_way, :string, comment: "权利取得方式"
    add_column :inno_works, :rights_belong_way, :string, comment: "权利归属方式"
    add_column :inno_works, :contact_name, :string, comment: "联系人"
    add_column :inno_works, :contact_mobile, :string, comment: "联系电话"
    add_column :inno_works, :convert_intention, :string, comment: "转化意向"
    add_column :inno_works, :convert_way, :string, comment: "转化意向"
    add_column :inno_works, :pub_state, :string, comment: "发表状态"
    add_column :inno_works, :payload, :jsonb, comment: "额外信息"
    remove_column :inno_works, :amount, :decimal, comment: "金额"
  end
end

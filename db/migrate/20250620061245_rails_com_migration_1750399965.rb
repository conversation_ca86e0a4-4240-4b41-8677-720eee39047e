class RailsComMigration1750399965 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_project_relations do |t|
      t.references :project
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
    end

    create_table :inno_tag_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "inno_tag_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "inno_tag_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "inno_tag_actions_uk_action_target_user"
    end

    create_table :inno_tags do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :ancestry, comment: "树形结构"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.string :type, comment: "STI类型"
      t.string :name, comment: "标签名称"
      t.integer :position, comment: "排序"
      t.timestamps
    end

    create_table :inno_projects do |t|
      t.references :app
      t.references :creator
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :name, comment: "项目名称"
      t.string :project_type, comment: "项目类型"
      t.string :leader_name, comment: "负责人姓名"
      t.decimal :contract_amount, comment: "合同金额"
      t.decimal :actual_amount, comment: "实到金额"
      t.jsonb :payload, comment: "信息"
      t.string :state
      t.timestamps
    end

    add_column :inno_patents, :pub_no, :string, comment: "公开号"
  end
end

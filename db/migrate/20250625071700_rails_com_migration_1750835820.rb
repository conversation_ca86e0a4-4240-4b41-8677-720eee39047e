class RailsComMigration1750835820 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_department_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "inno_department_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "inno_department_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "inno_department_actions_uk_action_target_user"
    end
    add_column :org_requests, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    add_column :member_requests, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    add_column :state_events, :type, :string, comment: "STI"
    add_column :state_events, :state, :string
    add_column :state_events, :state_attr_name, :string, comment: "状态机对应的模型属性名称"
    create_table :opm_relate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "opm_relate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "opm_relate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "opm_relate_actions_uk_action_target_user"
    end
    create_table :opm_holiday_travel_requests do |t|
      t.references :app
      t.references :user
      t.datetime :start_at, comment: "开始时间"
      t.datetime :end_at, comment: "结束时间"
      t.decimal :duration, comment: "时长"
      t.text :reason, comment: "理由"
      t.jsonb :attachment, comment: "附件"
      t.string :state
      t.boolean :is_workday, comment: "是否工作日"
      t.string :destination, comment: "目的地"
      t.string :contact_info, comment: "联系方式"
      t.string :report_type
      t.timestamps
    end
    create_table :opm_former_employees do |t|
      t.references :app
      t.references :org
      t.references :member
      t.references :user
      t.date :leave_at, comment: "离职时间"
      t.timestamps
    end
    create_table :opm_transactions do |t|
      t.references :user
      t.references :source, polymorphic: true
      t.string :category
      t.decimal :amount
      t.string :operation
      t.datetime :expires_at
      t.string :reason
      t.jsonb :details
      t.timestamps
    end
    create_table :opm_hr_transfers do |t|
      t.references :app
      t.references :org
      t.references :member
      t.references :user
      t.string :type, comment: "STI类型"
      t.jsonb :payload, comment: "payload"
      t.timestamps
    end
    create_table :opm_hr_requests do |t|
      t.references :app
      t.references :org
      t.references :ownership
      t.references :user
      t.references :member
      t.references :creator
      t.references :employ_invite
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.jsonb :payload, comment: "payload"
      t.timestamps
    end
    create_table :opm_employ_invites do |t|
      t.references :app
      t.references :creator
      t.references :org
      t.references :member_identity
      t.references :department
      t.references :duty
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.jsonb :payload
      t.timestamps
    end
    create_table :opm_records do |t|
      t.references :app
      t.references :user
      t.references :member
      t.references :source, polymorphic: true
      t.string :type, comment: "STI属性"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "编号"
      t.string :origin, comment: "来源"
      t.string :level, comment: "级别"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :opm_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "opm_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "opm_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "opm_permit_actions_uk_action_target_user"
    end
    create_table :opm_ownerships do |t|
      t.references :app
      t.references :org
      t.references :department
      t.references :department_identity
      t.references :member_identity
      t.references :duty
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :opm_job_titles do |t|
      t.references :app
      t.references :group
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :level, comment: "等级"
      t.string :code, comment: "编号"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :opm_items do |t|
      t.references :app
      t.references :user
      t.references :member
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :seq, comment: "编号"
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :content, comment: "内容"
      t.string :origin, comment: "来源"
      t.string :mode, comment: "分类"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :opm_groups do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "编号"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    create_table :opm_balances do |t|
      t.references :user
      t.string :category
      t.decimal :total
      t.datetime :expires_at
      t.string :reason
      t.jsonb :details
      t.timestamps
    end
    create_table :opm_leave_requests do |t|
      t.references :app
      t.references :user
      t.references :flowable, polymorphic: true
      t.string :type
      t.datetime :start_at
      t.datetime :end_at
      t.decimal :duration
      t.text :reason
      t.jsonb :attachment
      t.jsonb :model_payload
      t.string :leave_type
      t.string :unit_type
      t.decimal :min_unit
      t.decimal :max_unit
      t.decimal :min_interval
      t.timestamps
    end
    create_table :bpm_workflow_relations do |t|
      t.references :workflowable, polymorphic: true
      t.references :workflow
      t.timestamps
    end
    create_table :bpm_transitions do |t|
      t.references :workflow
      t.references :place
      t.string :type, comment: "STI"
      t.jsonb :callback_options, comment: "回调设置"
      t.jsonb :options, comment: "transition跳转的额外设置"
      t.timestamps
    end
    create_table :bpm_stars do |t|
      t.references :user
      t.references :workflow
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :bpm_place_relations do |t|
      t.references :workflow
      t.references :source
      t.references :target
      t.timestamps
    end
    create_table :bpm_instance_relations do |t|
      t.references :instance
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "随机数"
      t.string :model_setting_flag, comment: "对应模型的flag"
      t.timestamps
    end
    add_column :memberships, :create_instance_state, :string, comment: "创建工作流的状态"
    add_column :memberships, :create_instance_timestamp, :datetime, comment: "创建工作流的操作时间"
    add_reference :model_settings, :bpm_workflow
    add_reference :model_settings, :ref_model_setting
    add_column :model_settings, :ref_model_setting_flag, :string, comment: "关联model_setting_flag"
    create_table :bpm_tokens do |t|
      t.timestamps
    end
    create_table :bpm_places do |t|
      t.references :workflow
      t.string :type, comment: "STI"
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :desc, comment: "节点描述"
      t.integer :position, comment: "根据 tree 边生成的 position"
      t.boolean :is_summary, comment: "是否快捷引用"
      t.jsonb :fields, comment: "workflow form字段在这个place的权限，读写/可见"
      t.jsonb :place_form, comment: "动态表单的json字段"
      t.jsonb :options, comment: "节点的配置信息"
      t.jsonb :timer_options, comment: "节点时间配置"
      t.jsonb :trigger_options, comment: "token进出节点时候可能需要额外操作的内容"
      t.jsonb :token_actions, comment: "操作菜单配置"
      t.jsonb :layout_options, comment: "前端页面使用的配置"
      t.jsonb :activate_options, comment: "同步回调配置"
      t.jsonb :token_source_options, comment: "token place相关配置"
      t.jsonb :form_setting, comment: "表单配置"
      t.timestamps
    end
    create_table :bpm_rules do |t|
      t.references :workflow
      t.string :name, comment: "规则名称"
      t.integer :time_in_second, comment: "设定时间范围"
      t.string :type, comment: "STI"
      t.jsonb :options, comment: "具体配置内容"
      t.timestamps
    end
    create_table :bpm_workflows do |t|
      t.references :app
      t.references :creator
      t.references :mod
      t.references :catalog
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI"
      t.string :name, comment: "流程名称"
      t.text :desc, comment: "流程描述"
      t.jsonb :icon, comment: "流程图标"
      t.jsonb :cover_image, comment: "流程封面"
      t.string :state
      t.integer :position, comment: "catalog内排序"
      t.string :instance_type, comment: "自动生成的instance_type"
      t.string :classify
      t.jsonb :form, comment: "表单配置 "
      t.jsonb :meta, comment: "工作流额外配置信息 "
      t.jsonb :token_actions, comment: "操作菜单配置"
      t.jsonb :trigger_options, comment: "instance状态改变时候需要额外操作的内容"
      t.boolean :auto_complete_same_handle_token, comment: "是否跳过连续相同的审批人"
      t.jsonb :submit_options, comment: "限制条件"
      t.jsonb :form_setting, comment: "表单配置"
      t.boolean :enable_level, comment: "是否启用 instance 优先级"
      t.jsonb :level_options, comment: "优先级配置"
      t.jsonb :conf, comment: "其他配置"
      t.timestamps
    end
    create_table :bpm_instances do |t|
      t.references :app
      t.references :workflow
      t.references :creator
      t.references :flowable, polymorphic: true
      t.string :seq, comment: "编号"
      t.string :type, comment: "STI"
      t.jsonb :payload, comment: "流程表单"
      t.jsonb :storage, comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
      t.jsonb :summary, comment: "instance在列表页显示的内容"
      t.string :state, comment: "流程状态"
      t.string :flowable_flag, comment: "flowable不同流程的flag"
      t.integer :spent_time_in_second, comment: "耗时时长"
      t.jsonb :cache_payload, comment: "额外存储的结构，根据场合可以作为payload的存储"
      t.datetime :action_at, comment: "激活时间"
      t.jsonb :last_token_attr, comment: "最新token信息"
      t.string :level, comment: "流程级别"
      t.timestamps
    end
    add_reference :users, :supervisor
    add_column :users, :birthday, :date, comment: "生日"
    add_column :users, :political, :string, comment: "政治面貌"
    add_column :users, :education, :string, comment: "学位"
    add_column :users, :degree, :string, comment: "学历"
    create_table :bpm_catalogs do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :icon, comment: "图标"
      t.integer :position, comment: "排序"
      t.boolean :published, comment: "是否发布"
      t.timestamps
    end
  end
end

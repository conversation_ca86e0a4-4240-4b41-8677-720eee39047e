class RailsComMigration1746694494 < ActiveRecord::Migration[7.1]

  def change
    create_table :inno_scientific_researches do |t|
      t.references :app
      t.references :creator
      t.references :source, polymorphic: true
      t.string :name, comment: "课题名称"
      t.string :num, comment: "课题编号"
      t.string :leader, comment: "项目负责人"
      t.string :undertaking_unit, comment: "承担单位"
      t.string :level, comment: "项目级别"
      t.string :from, comment: "项目来源"
      t.string :kind, comment: "项目类型"
      t.date :init_at, comment: "立项时间"
      t.date :start_at, comment: "开始时间"
      t.date :end_at, comment: "结束时间"
      t.decimal :funds, comment: "项目经费"
      t.string :financial_support, comment: "财政课题资助"
      t.jsonb :payload
      t.timestamps
    end
    create_table :inno_competitive_products do |t|
      t.references :app
      t.references :creator
      t.references :source, polymorphic: true
      t.string :name, comment: "名称"
      t.string :company_name, comment: "生产企业"
      t.string :approval_no, comment: "批准文号"
      t.text :intro, comment: "简介"
      t.timestamps
    end
    create_table :inno_awards do |t|
      t.references :app
      t.references :creator
      t.references :source, polymorphic: true
      t.string :name, comment: "获奖名称"
      t.integer :year, comment: "年份"
      t.string :grant_unit, comment: "授奖单位"
      t.string :level, comment: "奖励级别"
      t.string :completer, comment: "完成人"
      t.timestamps
    end
    add_column :inno_works, :transforms_count, :integer, comment: "转化次数"
    add_column :inno_works, :stars_count, :integer
    add_column :inno_works, :amount, :decimal, comment: "金额"
    add_column :inno_software_copyrights, :transforms_count, :integer, comment: "转化次数"
    add_column :inno_software_copyrights, :stars_count, :integer
    add_column :inno_papers, :journal_name, :string, comment: "期刊名称"
    add_column :inno_papers, :jcr, :string, comment: "JCR分区"
    add_column :inno_papers, :year, :integer, comment: "发表年份"
    add_column :inno_papers, :pmid, :string, comment: "PMID"
    add_column :inno_papers, :intro, :text, comment: "简介"
    remove_column :inno_papers, :date, :string, comment: "日期"
  end
end

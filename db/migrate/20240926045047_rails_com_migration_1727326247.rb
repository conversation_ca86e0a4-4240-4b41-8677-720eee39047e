class RailsComMigration1727326247 < ActiveRecord::Migration[7.1]

  def change
    create_table :grant_applications do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "应用名称"
      t.string :type, comment: "STI类型"
      t.string :app_key, comment: "app key"
      t.string :app_secret, comment: "app secret"
      t.jsonb :options, comment: "额外的配置, { encrypt: sm 或 aes }"
      t.timestamps
    end
    create_table :disk_recent_items do |t|
      t.references :app
      t.references :source
      t.references :item
      t.references :creator
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
    end
    create_table :disk_sources do |t|
      t.references :app
      t.references :creator
      t.references :target, polymorphic: true
      t.jsonb :views, comment: "权限设置"
      t.boolean :view_enable, comment: "是否开启permit的按钮"
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.text :desc, comment: "描述"
      t.boolean :shared, comment: "是否开放"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :disk_tag_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "disk_tag_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "disk_tag_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "disk_tag_actions_uk_action_target_user"
    end
    create_table :disk_tags do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :ancestry, comment: "树形结构"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.string :type, comment: "STI类型"
      t.string :name, comment: "标签名称"
      t.integer :position, comment: "排序"
      t.boolean :recommend, comment: "是否推荐"
      t.string :color, comment: "标签颜色设置"
      t.timestamps
    end
    create_table :disk_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "disk_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "disk_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "disk_permit_actions_uk_action_target_user"
    end
    create_table :disk_items do |t|
      t.references :app
      t.references :creator
      t.references :source, polymorphic: true
      t.string :ancestry, comment: "树形结构"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :views, comment: "权限设置"
      t.boolean :view_enable, comment: "是否开启permit的按钮"
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "资源名称"
      t.text :desc, comment: "资源描述"
      t.string :type, comment: "STI"
      t.string :ftype, comment: "文件类型"
      t.jsonb :attachment, comment: "文件地址"
      t.boolean :read_only
      t.integer :position, comment: "排序"
      t.timestamps
    end
  end
end

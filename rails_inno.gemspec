require_relative "lib/rails_inno/version"

Gem::Specification.new do |spec|
  spec.name        = "rails_inno"
  spec.version     = RailsInno::VERSION
  spec.authors     = ["liyijie"]
  spec.email       = ["<EMAIL>"]
  spec.homepage    = "http://www.tallty.com"
  spec.summary     = "Summary of RailsInno."
  spec.description = "Description of RailsInno."
  spec.license     = "MIT"

  # Prevent pushing this gem to RubyGems.org. To allow pushes either set the "allowed_push_host"
  # to allow pushing to a single host or delete this section to allow pushing to any host.
  spec.metadata["allowed_push_host"] = "Set to 'http://mygemserver.com'"

  spec.metadata["homepage_uri"] = spec.homepage
  spec.metadata["source_code_uri"] = "http://www.tallty.com"
  spec.metadata["changelog_uri"] = "http://www.tallty.com"

  spec.files = Dir.chdir(File.expand_path(__dir__)) do
    Dir["{app,config,db,lib}/**/*", "MIT-LICENSE", "Rakefile", "README.md"]
  end

  spec.add_dependency "rails", ">= *******"

  spec.add_dependency 'rack-cors'
  spec.add_dependency 'jbuilder'

  spec.add_development_dependency 'annotate'
  spec.add_development_dependency 'pry-doc'
  spec.add_development_dependency 'pry-rails'
  spec.add_development_dependency 'awesome_print'
  spec.add_development_dependency 'better_errors'
  spec.add_development_dependency 'binding_of_caller'
  spec.add_development_dependency 'hirb'
  spec.add_development_dependency 'sassc'
end

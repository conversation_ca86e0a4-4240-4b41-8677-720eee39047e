require 'swagger_helper'

RSpec.describe 'inno/user/transforms', type: :request, capture_examples: true, tags: ["inno user"] do
  transform_ref = {
    type: :object, properties: {
      transform: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          state: { type: :string, description: '状态属性' },
          contract_amount: { type: :decimal, description: '合同金额' },
          actual_amount: { type: :decimal, description: '实到金额' },
          payload: { type: :jsonb, description: '额外信息' },
        }
      }
    }
  }
  transform_value = FactoryBot.attributes_for(:inno_transform)

  before :each do
  end

  path '/inno/user/transforms' do

    get(summary: 'list transforms') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_transform_count
        }
      end
    end
  end

  # path '/inno/user/transforms/list_index' do

  #   post(summary: 'list_index transform') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :transform, in: :body, schema: transform_ref
  #     response(201, description: 'successful') do
  #       let(:transform) do
  #         { transform: transform_value }
  #       end
  #       it {
  #         body = JSON.parse(response.body)
  #         expect(body['records'].count).to eq @inno_transform_count
  #       }
  #     end
  #   end
  # end

  # path '/inno/user/transforms/group_index' do

  #   post(summary: 'group_index transform') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :transform, in: :body, schema: transform_ref
  #     response(201, description: 'successful') do
  #       let(:transform) do
  #         { transform: transform_value }
  #       end
  #     end
  #   end
  # end

  path '/inno/user/transforms/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show transform') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_transforms.first.id }
        it {
          body = JSON.parse(response.body)
          transform = @inno_transforms.first
          expect(body['app_id']).to eq transform.app_id
          expect(body['source_type']).to eq transform.source_type
          expect(body['source_id']).to eq transform.source_id
          expect(body['state']).to eq transform.state
          expect(body['contract_amount']).to eq transform.contract_amount
          expect(body['actual_amount']).to eq transform.actual_amount.to_s
          expect(body['payload']).to eq transform.payload
        }
      end
    end
  end
end

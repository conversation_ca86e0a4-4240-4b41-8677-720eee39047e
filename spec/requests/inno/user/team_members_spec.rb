require 'swagger_helper'

RSpec.describe 'inno/user/team_members', type: :request, capture_examples: true, tags: ["inno user"] do
  team_member_ref = {
    type: :object, properties: {
      team_member: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          department_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          role: { type: :string, description: '团队角色' },
        }
      }
    }
  }
  team_member_value = FactoryBot.attributes_for(:inno_team_member)

  before :each do
    team_member_value = team_member_value.merge(
      source_id: @inno_patent.id,
      source_type: 'Inno::Patent',
      user_id: @user.id,
      department_id: @department.id
    )
  end

  path '/inno/user/team_members' do

    get(summary: 'list team_members') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_team_member_count
        }
      end
    end

    post(summary: 'create team_member') do
      produces 'application/json'
      consumes 'application/json'
      parameter :team_member, in: :body, schema: team_member_ref
      response(422, description: 'failed') do
        let(:team_member) do
          { team_member: team_member_value }
        end
        it {
          body = JSON.parse(response.body)
          # expect(body['app_id']).to eq @user.id
          # expect(body['source_type']).to eq team_member_value[:source_type]
          # expect(body['source_id']).to eq team_member_value[:source_id]
          # expect(body['user_id']).to eq team_member_value[:user_id]
          # expect(body['department_id']).to eq team_member_value[:department_id]
          # expect(body['creator_id']).to eq team_member_value[:creator_id]
          # expect(body['role']).to eq team_member_value[:role]
        }
      end
    end
  end


  path '/inno/user/team_members/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show team_member') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_team_members.first.id }
        it {
          body = JSON.parse(response.body)
          team_member = @inno_team_members.first
          expect(body['app_id']).to eq team_member.app_id
          expect(body['source_type']).to eq team_member.source_type
          expect(body['source_id']).to eq team_member.source_id
          expect(body['user_id']).to eq team_member.user_id
          expect(body['department_id']).to eq team_member.department_id
          expect(body['creator_id']).to eq team_member.creator_id
          expect(body['role']).to eq team_member.role
        }
      end
    end

    patch(summary: 'update team_member') do
      produces 'application/json'
      consumes 'application/json'
      parameter :team_member, in: :body, schema: team_member_ref
      response(201, description: 'successful') do
        let(:id) { @inno_team_members.first.id }
        let(:team_member) do
          { team_member: team_member_value }
        end
      end
    end

    delete(summary: 'delete team_member') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_team_members.first.id }
        it {
          expect(Inno::TeamMember.count).to eq(@inno_team_member_count-1)
        }
      end
    end
  end
end

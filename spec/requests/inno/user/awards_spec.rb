require 'swagger_helper'

RSpec.describe 'inno/user/awards', type: :request, capture_examples: true, tags: ["inno user"] do
  award_ref = {
    type: :object, properties: {
      award: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '获奖名称' },
          year: { type: :integer, description: '年份' },
          grant_unit: { type: :string, description: '授奖单位' },
          level: { type: :string, description: '奖励级别' },
          completer: { type: :string, description: '完成人' },
        }
      }
    }
  }
  award_value = FactoryBot.attributes_for(:inno_award)

  before :each do
    award_value = award_value.merge(source_type: 'Inno::Patent', source_id: @inno_patent.id)
  end

  path '/inno/user/awards' do

    get(summary: 'list awards') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_award_count
        }
      end
    end

    post(summary: 'create award') do
      produces 'application/json'
      consumes 'application/json'
      parameter :award, in: :body, schema: award_ref
      response(201, description: 'successful') do
        let(:award) do
          { award: award_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @user.app_id
          expect(body['creator_id']).to eq @user.id
          expect(body['source_type']).to eq award_value[:source_type]
          expect(body['source_id']).to eq award_value[:source_id]
          expect(body['name']).to eq award_value['name']
          expect(body['year']).to eq award_value[:year]
          expect(body['grant_unit']).to eq award_value[:grant_unit]
          expect(body['level']).to eq award_value[:level]
          expect(body['completer']).to eq award_value[:completer]
        }
      end
    end
  end

  path '/inno/user/awards/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show award') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_awards.first.id }
        it {
          body = JSON.parse(response.body)
          award = @inno_awards.first
          expect(body['app_id']).to eq award.app_id
          expect(body['creator_id']).to eq award.creator_id
          expect(body['source_type']).to eq award.source_type
          expect(body['source_id']).to eq award.source_id
          expect(body['name']).to eq award.name
          expect(body['year']).to eq award.year
          expect(body['grant_unit']).to eq award.grant_unit
          expect(body['level']).to eq award.level
          expect(body['completer']).to eq award.completer
        }
      end
    end

    patch(summary: 'update award') do
      produces 'application/json'
      consumes 'application/json'
      parameter :award, in: :body, schema: award_ref
      response(201, description: 'successful') do
        let(:id) { @inno_awards.first.id }
        let(:award) do
          { award: award_value }
        end
      end
    end

    delete(summary: 'delete award') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_awards.first.id }
        it {
          expect(Inno::Award.count).to eq(@inno_award_count-1)
        }
      end
    end
  end
end

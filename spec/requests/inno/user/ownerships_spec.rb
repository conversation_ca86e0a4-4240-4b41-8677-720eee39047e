require 'swagger_helper'

RSpec.describe 'inno/user/ownerships', type: :request, capture_examples: true, tags: ["inno user"] do
  ownership_ref = {
    type: :object, properties: {
      ownership: {
        type: :object, properties: {
          user_type: { type: :string, description: '' },
          user_id: { type: :integer, description: '' },
          role: { type: :string, description: '角色' },
        }
      }
    }
  }
  ownership_value = FactoryBot.attributes_for(:inno_ownership)

  before :each do
  end

  path '/inno/user/patents/{patent_id}/ownerships' do
    parameter 'patent_id', in: :path, type: :string

    get(summary: 'list ownerships') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:patent_id) { @inno_patent.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_ownership_count
        }
      end
    end

  #   post(summary: 'create ownership') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :ownership, in: :body, schema: ownership_ref
  #     response(201, description: 'successful') do
  #       let(:patent_id) { @inno_patent.id }
  #       let(:ownership) do
  #         { ownership: ownership_value.merge(user_type: @user.class.name, user_id: @user.id) }
  #       end
  #       it {
  #         body = JSON.parse(response.body)
  #         expect(body['app_id']).to eq @inno_patent.app_id
  #         expect(body['resource_type']).to eq  @inno_patent.class.name
  #         expect(body['resource_id']).to eq  @inno_patent.id
  #         expect(body['user_type']).to eq @user.class.name
  #         expect(body['user_id']).to eq @user.id
  #         expect(body['role']).to eq ownership_value['role']
  #       }
  #     end
  #   end
  end

  path '/inno/user/patents/{patent_id}/ownerships/{id}' do
    parameter 'patent_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show ownership') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:patent_id) { @inno_patent.id }
        let(:id) { @inno_ownerships.first.id }
        it {
          body = JSON.parse(response.body)
          ownership = @inno_ownerships.first
          expect(body['app_id']).to eq ownership.app_id
          expect(body['resource_type']).to eq ownership.resource_type
          expect(body['resource_id']).to eq ownership.resource_id
          expect(body['user_type']).to eq ownership.user_type
          expect(body['user_id']).to eq ownership.user_id
          expect(body['role']).to eq ownership.role
        }
      end
    end

    # patch(summary: 'update ownership') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   parameter :ownership, in: :body, schema: ownership_ref
    #   response(201, description: 'successful') do
    #     let(:patent_id) { @inno_patent.id }
    #     let(:id) { @inno_ownerships.first.id }
    #     let(:ownership) do
    #       { ownership: ownership_value.merge(user_type: @user.class.name, user_id: @user.id) }
    #     end
    #   end
    # end

    # delete(summary: 'delete ownership') do
    #   produces 'application/json'
    #   consumes 'application/json'
    #   response(204, description: 'successful') do
    #     let(:patent_id) { @inno_patent.id }
    #     let(:id) { @inno_ownerships.first.id }
    #     it {
    #       expect(Inno::Ownership.count).to eq(@inno_ownership_count-1)
    #     }
    #   end
    # end
  end
end

require 'swagger_helper'

RSpec.describe 'inno/user/settings', type: :request, capture_examples: true, tags: ["inno user"] do
  setting_ref = {
    type: :object, properties: {
      setting: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          position: { type: :integer, description: '排序' },
          option: { type: :jsonb, description: '配置' },
        }
      }
    }
  }
  setting_value = FactoryBot.attributes_for(:inno_setting)

  before :each do
  end

  path '/inno/user/settings' do

    get(summary: 'list settings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_setting_count
        }
      end
    end

    post(summary: 'create setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :setting, in: :body, schema: setting_ref
      response(201, description: 'successful') do
        let(:setting) do
          { setting: setting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq setting_value[:name]
          expect(body['state']).to eq setting_value[:state]
          expect(body['position']).to eq setting_value[:position]
          expect(body['option']).not_to be_nil
        }
      end
    end
  end

  path '/inno/user/settings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show setting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_settings.first.id }
        it {
          body = JSON.parse(response.body)
          setting = @inno_settings.first
          expect(body['app_id']).to eq setting.app_id
          expect(body['name']).to eq setting.name
          expect(body['state']).to eq setting.state
          expect(body['position']).to eq setting.position
          expect(body['option']).not_to be_nil
        }
      end
    end

    patch(summary: 'update setting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :setting, in: :body, schema: setting_ref
      response(201, description: 'successful') do
        let(:id) { @inno_settings.first.id }
        let(:setting) do
          { setting: setting_value }
        end
      end
    end

    delete(summary: 'delete setting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_settings.first.id }
        it {
          expect(Inno::Setting.count).to eq(@inno_setting_count-1)
        }
      end
    end
  end
end

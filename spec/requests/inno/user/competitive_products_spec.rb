require 'swagger_helper'

RSpec.describe 'inno/user/competitive_products', type: :request, capture_examples: true, tags: ["inno user"] do
  competitive_product_ref = {
    type: :object, properties: {
      competitive_product: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          company_name: { type: :string, description: '生产企业' },
          approval_no: { type: :string, description: '批准文号' },
          intro: { type: :text, description: '简介' },
        }
      }
    }
  }
  competitive_product_value = FactoryBot.attributes_for(:inno_competitive_product)

  before :each do
    competitive_product_value = competitive_product_value.merge(source_type: 'Inno::Patent', source_id: @inno_patent.id)
  end

  path '/inno/user/competitive_products' do

    get(summary: 'list competitive_products') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_competitive_product_count
        }
      end
    end

    post(summary: 'create competitive_product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :competitive_product, in: :body, schema: competitive_product_ref
      response(201, description: 'successful') do
        let(:competitive_product) do
          { competitive_product: competitive_product_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @user.app_id
          expect(body['creator_id']).to eq @user.id
          expect(body['source_type']).to eq competitive_product_value[:source_type]
          expect(body['source_id']).to eq competitive_product_value[:source_id]
          expect(body['name']).to eq competitive_product_value['name']
          expect(body['company_name']).to eq competitive_product_value[:company_name]
          expect(body['approval_no']).to eq competitive_product_value[:approval_no]
          expect(body['intro']).to eq competitive_product_value[:intro]
        }
      end
    end
  end


  path '/inno/user/competitive_products/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show competitive_product') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_competitive_products.first.id }
        it {
          body = JSON.parse(response.body)
          competitive_product = @inno_competitive_products.first
          expect(body['app_id']).to eq competitive_product.app_id
          expect(body['creator_id']).to eq competitive_product.creator_id
          expect(body['source_type']).to eq competitive_product.source_type
          expect(body['source_id']).to eq competitive_product.source_id
          expect(body['name']).to eq competitive_product.name
          expect(body['company_name']).to eq competitive_product.company_name
          expect(body['approval_no']).to eq competitive_product.approval_no
          expect(body['intro']).to eq competitive_product.intro
        }
      end
    end

    patch(summary: 'update competitive_product') do
      produces 'application/json'
      consumes 'application/json'
      parameter :competitive_product, in: :body, schema: competitive_product_ref
      response(201, description: 'successful') do
        let(:id) { @inno_competitive_products.first.id }
        let(:competitive_product) do
          { competitive_product: competitive_product_value }
        end
      end
    end

    delete(summary: 'delete competitive_product') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_competitive_products.first.id }
        it {
          expect(Inno::CompetitiveProduct.count).to eq(@inno_competitive_product_count-1)
        }
      end
    end
  end
end

require 'swagger_helper'

RSpec.describe 'inno/user/payments', type: :request, capture_examples: true, tags: ["inno user"] do
  payment_ref = {
    type: :object, properties: {
      payment: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          patent_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          seq: { type: :string, description: '编号' },
          start_date: { type: :date, description: '生效时间' },
          end_date: { type: :date, description: '失效时间' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '' },
          amount: { type: :decimal, description: '金额' },
          paid_at: { type: :datetime, description: '支付时间' },
          paid_type: { type: :string, description: '支付方式' },
          attachment: { type: :jsonb, description: '附件' },
          payload: { type: :jsonb, description: '其他字段' },
        }
      }
    }
  }
  payment_value = FactoryBot.attributes_for(:inno_payment)

  before :each do
    @patent = @inno_patent
  end

  path '/inno/user/patents/{patent_id}/payments' do
    parameter 'patent_id', in: :path, type: :string

    get(summary: 'list payments') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:patent_id) { @patent.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_payment_count
        }
      end
    end

    post(summary: 'create payment') do
      produces 'application/json'
      consumes 'application/json'
      parameter :payment, in: :body, schema: payment_ref
      response(201, description: 'successful') do
        let(:patent_id) { @patent.id }
        let(:payment) do
          { payment: payment_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['source_type']).to eq payment_value[:source_type]
          expect(body['source_id']).to eq payment_value[:source_id]
          expect(body['patent_id']).to eq @patent.id
          expect(body['user_id']).to eq @user.id
          expect(body['type']).to eq payment_value[:type]
          expect(body['seq']).not_to be_nil
          expect(body['start_date']).to eq payment_value[:start_date]
          expect(body['end_date']).to eq payment_value[:end_date]
          expect(body['name']).to eq payment_value[:name]
          expect(body['state']).to eq payment_value[:state]
          expect(body['amount']).to eq payment_value[:amount]
          expect(body['paid_at']).to eq payment_value[:paid_at]
          expect(body['paid_type']).to eq payment_value[:paid_type]
          expect(body['attachment']).to eq payment_value[:attachment]
          expect(body['payload']).to eq payment_value[:payload]
        }
      end
    end
  end

  path '/inno/user/patents/{patent_id}/payments/{id}' do
    parameter 'patent_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show payment') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:patent_id) { @patent.id }
        let(:id) { @inno_payments.first.id }
        it {
          body = JSON.parse(response.body)
          payment = @inno_payments.first
          expect(body['app_id']).to eq payment.app_id
          expect(body['source_type']).to eq payment.source_type
          expect(body['source_id']).to eq payment.source_id
          expect(body['patent_id']).to eq payment.patent_id
          expect(body['user_id']).to eq payment.user_id
          expect(body['type']).to eq payment.type
          expect(body['seq']).to eq payment.seq
          expect(body['start_date']).to eq payment.start_date
          expect(body['end_date']).to eq payment.end_date
          expect(body['name']).to eq payment.name
          expect(body['state']).to eq payment.state
          expect(body['amount']).to eq payment.amount
          expect(body['paid_at']).to eq payment.paid_at
          expect(body['paid_type']).to eq payment.paid_type
          expect(body['attachment']).to eq payment.attachment
          expect(body['payload']).to eq payment.payload
        }
      end
    end

    patch(summary: 'update payment') do
      produces 'application/json'
      consumes 'application/json'
      parameter :payment, in: :body, schema: payment_ref
      response(201, description: 'successful') do
        let(:patent_id) { @patent.id }
        let(:id) { @inno_payments.first.id }
        let(:payment) do
          { payment: payment_value }
        end
      end
    end

    delete(summary: 'delete payment') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:patent_id) { @patent.id }
        let(:id) { @inno_payments.first.id }
        it {
          expect(Inno::Payment.count).to eq(@inno_payment_count-1)
        }
      end
    end
  end
end

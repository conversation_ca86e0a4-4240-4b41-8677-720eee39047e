require 'swagger_helper'

RSpec.describe 'inno/user/papers', type: :request, capture_examples: true, tags: ["inno user"] do
  paper_ref = {
    type: :object, properties: {
      paper: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '论文名称' },
        }
      }
    }
  }
  paper_value = FactoryBot.attributes_for(:inno_paper)

  before :each do
  end

  path '/inno/user/papers' do

    get(summary: 'list papers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_paper_count
        }
      end
    end

    post(summary: 'create paper') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paper, in: :body, schema: paper_ref
      response(201, description: 'successful') do
        let(:paper) do
          { paper: paper_value.merge(source_type: @inno_patent.class.name, source_id: @inno_patent.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @inno_patent.app_id
          expect(body['creator_id']).to eq @user.id
          expect(body['source_type']).to eq  @inno_patent.class.name
          expect(body['source_id']).to eq @inno_patent.id
          expect(body['name']).to eq paper_value['name']
        }
      end
    end
  end

  path '/inno/user/papers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show paper') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_papers.first.id }
        it {
          body = JSON.parse(response.body)
          paper = @inno_papers.first
          expect(body['app_id']).to eq paper.app_id
          expect(body['creator_id']).to eq paper.creator_id
          expect(body['source_type']).to eq paper.source_type
          expect(body['source_id']).to eq paper.source_id
          expect(body['name']).to eq paper.name
        }
      end
    end

    patch(summary: 'update paper') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paper, in: :body, schema: paper_ref
      response(201, description: 'successful') do
        let(:id) { @inno_papers.first.id }
        let(:paper) do
          { paper: { name: paper_value['name'] } }
        end

        it {
          body = JSON.parse(response.body)
        }
      end
    end

    delete(summary: 'delete paper') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_papers.first.id }
        it {
          expect(Inno::Paper.count).to eq(@inno_paper_count-1)
        }
      end
    end
  end
end

require 'swagger_helper'

RSpec.describe 'inno/user/software_copyrights', type: :request, capture_examples: true, tags: ["inno user"] do
  software_copyright_ref = {
    type: :object, properties: {
      software_copyright: {
        type: :object, properties: {
          name: { type: :string, description: '软件名称' },
          serial_no: { type: :string, description: '流水号' },
          reg_no: { type: :string, description: '登记号' },
          owner: { type: :string, description: '著作权人' },
          right_way: { type: :string, description: '权利取得方式' },
          right_range: { type: :string, description: '权利防伪' },
          cert_no: { type: :string, description: '证书号' },
          cert_date: { type: :date, description: '发证时间' },
        }
      }
    }
  }
  software_copyright_value = FactoryBot.attributes_for(:inno_software_copyright)

  before :each do
  end

  path '/inno/user/software_copyrights' do

    get(summary: 'list software_copyrights') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_software_copyright_count
        }
      end
    end

    post(summary: 'create software_copyright') do
      produces 'application/json'
      consumes 'application/json'
      parameter :software_copyright, in: :body, schema: software_copyright_ref
      response(201, description: 'successful') do
        let(:software_copyright) do
          { software_copyright: software_copyright_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['name']).to eq software_copyright_value['name']
          expect(body['serial_no']).to eq software_copyright_value[:serial_no]
          expect(body['reg_no']).to eq software_copyright_value[:reg_no]
          expect(body['owner']).to eq software_copyright_value[:owner]
          expect(body['right_way']).to eq software_copyright_value[:right_way]
          expect(body['right_range']).to eq software_copyright_value[:right_range]
          expect(body['cert_no']).to eq software_copyright_value[:cert_no]
          expect(body['cert_date']).to eq software_copyright_value[:cert_date]
        }
      end
    end
  end

  path '/inno/user/software_copyrights/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show software_copyright') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_software_copyrights.first.id }
        it {
          body = JSON.parse(response.body)
          software_copyright = @inno_software_copyrights.first
          expect(body['name']).to eq software_copyright.name
          expect(body['serial_no']).to eq software_copyright.serial_no
          expect(body['reg_no']).to eq software_copyright.reg_no
          expect(body['owner']).to eq software_copyright.owner
          expect(body['right_way']).to eq software_copyright.right_way
          expect(body['right_range']).to eq software_copyright.right_range
          expect(body['cert_no']).to eq software_copyright.cert_no
          expect(body['cert_date']).to eq software_copyright.cert_date
        }
      end
    end

    patch(summary: 'update software_copyright') do
      produces 'application/json'
      consumes 'application/json'
      parameter :software_copyright, in: :body, schema: software_copyright_ref
      response(201, description: 'successful') do
        let(:id) { @inno_software_copyrights.first.id }
        let(:software_copyright) do
          { software_copyright: software_copyright_value }
        end
      end
    end

    delete(summary: 'delete software_copyright') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_software_copyrights.first.id }
        it {
          expect(Inno::SoftwareCopyright.count).to eq(@inno_software_copyright_count-1)
        }
      end
    end
  end
end

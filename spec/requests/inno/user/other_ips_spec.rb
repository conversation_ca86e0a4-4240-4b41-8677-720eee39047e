require 'swagger_helper'

RSpec.describe 'inno/user/other_ips', type: :request, capture_examples: true, tags: ["inno user"] do
  other_ip_ref = {
    type: :object, properties: {
      other_ip: {
        type: :object, properties: {
          name: { type: :string, description: '名称' },
          kind: { type: :string, description: '类型' },
          grant_date: { type: :date, description: '授权日' },
          apply_date: { type: :date, description: '申请日' },
          apply_no: { type: :string, description: '申请号' },
          cert_no: { type: :string, description: '证书号' },
          obligee: { type: :string, description: '权利人' },
          inventor: { type: :string, description: '发明人/设计人' },
          agency: { type: :string, description: '代理机构' },
          contact_name: { type: :string, description: '联系人' },
          contact_mobile: { type: :string, description: '联系方式' },
          convert_intention: { type: :string, description: '转化意向' },
          convert_way: { type: :string, description: '转化方式' },
          payload: { type: :jsonb, description: '' },
        }
      }
    }
  }
  other_ip_value = FactoryBot.attributes_for(:inno_other_ip)

  before :each do
  end

  path '/inno/user/other_ips' do

    get(summary: 'list other_ips') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_other_ip_count
        }
      end
    end

    post(summary: 'create other_ip') do
      produces 'application/json'
      consumes 'application/json'
      parameter :other_ip, in: :body, schema: other_ip_ref
      response(201, description: 'successful') do
        let(:other_ip) do
          { other_ip: other_ip_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['creator_id']).to eq @user.id
          expect(body['app_id']).to eq @user.app_id
          expect(body['name']).to eq other_ip_value['name']
          expect(body['kind']).to eq other_ip_value[:kind]
          expect(body['grant_date']).to eq other_ip_value[:grant_date]
          expect(body['apply_date']).to eq other_ip_value[:apply_date]
          expect(body['apply_no']).to eq other_ip_value[:apply_no]
          expect(body['cert_no']).to eq other_ip_value[:cert_no]
          expect(body['obligee']).to eq other_ip_value[:obligee]
          expect(body['inventor']).to eq other_ip_value[:inventor]
          expect(body['agency']).to eq other_ip_value[:agency]
          expect(body['contact_name']).to eq other_ip_value[:contact_name]
          expect(body['contact_mobile']).to eq other_ip_value[:contact_mobile]
          expect(body['convert_intention']).to eq other_ip_value[:convert_intention]
          expect(body['convert_way']).to eq other_ip_value[:convert_way]
          expect(body['payload']).to eq other_ip_value[:payload]
        }
      end
    end
  end

  path '/inno/user/other_ips/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show other_ip') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_other_ips.first.id }
        it {
          body = JSON.parse(response.body)
          other_ip = @inno_other_ips.first
          expect(body['creator_id']).to eq other_ip.creator_id
          expect(body['app_id']).to eq other_ip.app_id
          expect(body['transforms_count']).to eq other_ip.transforms_count
          expect(body['transform_state']).to eq other_ip.transform_state
          expect(body['stars_count']).to eq other_ip.stars_count
          expect(body['name']).to eq other_ip.name
          expect(body['kind']).to eq other_ip.kind
          expect(body['grant_date']).to eq other_ip.grant_date
          expect(body['apply_date']).to eq other_ip.apply_date
          expect(body['apply_no']).to eq other_ip.apply_no
          expect(body['cert_no']).to eq other_ip.cert_no
          expect(body['obligee']).to eq other_ip.obligee
          expect(body['inventor']).to eq other_ip.inventor
          expect(body['agency']).to eq other_ip.agency
          expect(body['contact_name']).to eq other_ip.contact_name
          expect(body['contact_mobile']).to eq other_ip.contact_mobile
          expect(body['convert_intention']).to eq other_ip.convert_intention
          expect(body['convert_way']).to eq other_ip.convert_way
          expect(body['payload']).to eq other_ip.payload
        }
      end
    end

    patch(summary: 'update other_ip') do
      produces 'application/json'
      consumes 'application/json'
      parameter :other_ip, in: :body, schema: other_ip_ref
      response(201, description: 'successful') do
        let(:id) { @inno_other_ips.first.id }
        let(:other_ip) do
          { other_ip: other_ip_value }
        end
      end
    end
  end
end

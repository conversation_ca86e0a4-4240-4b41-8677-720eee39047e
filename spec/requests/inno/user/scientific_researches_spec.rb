require 'swagger_helper'

RSpec.describe 'inno/user/scientific_researches', type: :request, capture_examples: true, tags: ["inno user"] do
  scientific_research_ref = {
    type: :object, properties: {
      scientific_research: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '课题名称' },
          num: { type: :string, description: '课题编号' },
          leader: { type: :string, description: '项目负责人' },
          undertaking_unit: { type: :string, description: '承担单位' },
          level: { type: :string, description: '项目级别' },
          from: { type: :string, description: '项目来源' },
          kind: { type: :string, description: '项目类型' },
          init_at: { type: :date, description: '立项时间' },
          start_at: { type: :date, description: '开始时间' },
          end_at: { type: :date, description: '结束时间' },
          funds: { type: :decimal, description: '项目经费' },
          financial_support: { type: :string, description: '财政课题资助' },
          payload: { type: :jsonb, description: '' },
        }
      }
    }
  }
  scientific_research_value = FactoryBot.attributes_for(:inno_scientific_research)

  before :each do
    scientific_research_value = scientific_research_value.merge(source_id: @inno_patent.id, source_type: 'Inno::Patent')
  end

  path '/inno/user/scientific_researches' do

    get(summary: 'list scientific_researches') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_scientific_research_count
        }
      end
    end

    post(summary: 'create scientific_research') do
      produces 'application/json'
      consumes 'application/json'
      parameter :scientific_research, in: :body, schema: scientific_research_ref
      response(201, description: 'successful') do
        let(:scientific_research) do
          { scientific_research: scientific_research_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @user.app_id
          expect(body['creator_id']).to eq @user.id
          expect(body['source_type']).to eq scientific_research_value[:source_type]
          expect(body['source_id']).to eq scientific_research_value[:source_id]
          expect(body['name']).to eq scientific_research_value['name']
          expect(body['num']).to eq scientific_research_value[:num]
          expect(body['leader']).to eq scientific_research_value[:leader]
          expect(body['undertaking_unit']).to eq scientific_research_value[:undertaking_unit]
          expect(body['level']).to eq scientific_research_value[:level]
          expect(body['from']).to eq scientific_research_value[:from]
          expect(body['kind']).to eq scientific_research_value[:kind]
          expect(body['init_at']).to eq scientific_research_value[:init_at]
          expect(body['start_at']).to eq scientific_research_value[:start_at]
          expect(body['end_at']).to eq scientific_research_value[:end_at]
          expect(body['funds']).to eq scientific_research_value[:funds]
          expect(body['financial_support']).to eq scientific_research_value[:financial_support]
          expect(body['payload']).to eq scientific_research_value[:payload]
        }
      end
    end
  end

  path '/inno/user/scientific_researches/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show scientific_research') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_scientific_researches.first.id }
        it {
          body = JSON.parse(response.body)
          scientific_research = @inno_scientific_researches.first
          expect(body['app_id']).to eq scientific_research.app_id
          expect(body['creator_id']).to eq scientific_research.creator_id
          expect(body['source_type']).to eq scientific_research.source_type
          expect(body['source_id']).to eq scientific_research.source_id
          expect(body['name']).to eq scientific_research.name
          expect(body['num']).to eq scientific_research.num
          expect(body['leader']).to eq scientific_research.leader
          expect(body['undertaking_unit']).to eq scientific_research.undertaking_unit
          expect(body['level']).to eq scientific_research.level
          expect(body['from']).to eq scientific_research.from
          expect(body['kind']).to eq scientific_research.kind
          expect(body['init_at']).to eq scientific_research.init_at
          expect(body['start_at']).to eq scientific_research.start_at
          expect(body['end_at']).to eq scientific_research.end_at
          expect(body['funds']).to eq scientific_research.funds
          expect(body['financial_support']).to eq scientific_research.financial_support
          expect(body['payload']).to eq scientific_research.payload
        }
      end
    end

    patch(summary: 'update scientific_research') do
      produces 'application/json'
      consumes 'application/json'
      parameter :scientific_research, in: :body, schema: scientific_research_ref
      response(201, description: 'successful') do
        let(:id) { @inno_scientific_researches.first.id }
        let(:scientific_research) do
          { scientific_research: scientific_research_value }
        end
      end
    end

    delete(summary: 'delete scientific_research') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_scientific_researches.first.id }
        it {
          expect(Inno::ScientificResearch.count).to eq(@inno_scientific_research_count-1)
        }
      end
    end
  end
end

require 'swagger_helper'

RSpec.describe 'inno/user/patents', type: :request, capture_examples: true, tags: ["inno user"] do
  patent_ref = {
    type: :object, properties: {
      patent: {
        type: :object, properties: {
          payload: { type: :jsonb, description: '额外信息' },
        }
      }
    }
  }
  patent_value = FactoryBot.attributes_for(:inno_patent)

  before :each do
    @user.add_role(:inno_admin)
  end

  path '/inno/user/patents' do

    get(summary: 'list patents') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_patent_count
        }
      end
    end
  end

  # path '/inno/user/patents/list_index' do

  #   post(summary: 'list_index patent') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :patent, in: :body, schema: patent_ref
  #     response(201, description: 'successful') do
  #       let(:patent) do
  #         { patent: patent_value }
  #       end
  #       it {
  #         body = JSON.parse(response.body)
  #         expect(body['records'].count).to eq @inno_patent_count
  #       }
  #     end
  #   end
  # end

  # path '/inno/user/patents/group_index' do

  #   post(summary: 'group_index patent') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :patent, in: :body, schema: patent_ref
  #     response(201, description: 'successful') do
  #       let(:patent) do
  #         { patent: patent_value }
  #       end
  #     end
  #   end
  # end

  path '/inno/user/patents/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show patent') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_patents.first.id }
        it {
          body = JSON.parse(response.body)
          patent = @inno_patents.first
          expect(body['app_id']).to eq patent.app_id
          expect(body['name']).to eq patent.name
          expect(body['apply_no']).to eq patent.apply_no
          expect(body['apply_date']).to eq patent.apply_date
          expect(body['pub_no']).to eq patent.pub_no
          expect(body['pub_date']).to eq patent.pub_date
          expect(body['abstract']).to eq patent.abstract
          expect(body['applicant']).to eq patent.applicant
          expect(body['investors']).to eq patent.investors
          expect(body['ipc']).to eq patent.ipc
          expect(body['patent_type']).to eq patent.patent_type
          expect(body['legal_state']).to eq patent.legal_state
          expect(body['transform_state']).to eq patent.transform_state
          expect(body['payload']).to eq patent.payload
        }
      end
    end

    patch(summary: 'update patent') do
      produces 'application/json'
      consumes 'application/json'
      parameter :patent, in: :body, schema: patent_ref
      response(201, description: 'successful') do
        let(:id) { @inno_patents.first.id }
        let(:patent) do
          { patent: patent_value }
        end
      end
    end
  end
end

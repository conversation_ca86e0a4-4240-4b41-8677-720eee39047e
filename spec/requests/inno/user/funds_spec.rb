require 'swagger_helper'

RSpec.describe 'inno/user/funds', type: :request, capture_examples: true, tags: ["inno user"] do
  fund_ref = {
    type: :object, properties: {
      fund: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          project_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          seq: { type: :string, description: '编号' },
          planned_at: { type: :datetime, description: '' },
          is_planned: { type: :boolean, description: '' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          amount: { type: :decimal, description: '金额' },
          position: { type: :integer, description: '排序' },
          payload: { type: :jsonb, description: '其他字段' },
          attachment: { type: :jsonb, description: '附件' },
          received_amount: { type: :decimal, description: '到账金额' },
          received_date: { type: :date, description: '收款日期' },
          cost_amount: { type: :decimal, description: '成本' },
          income_amount: { type: :decimal, description: '收入' },
          profit_amount: { type: :decimal, description: '收益' },
          tech_operator_amount: { type: :decimal, description: '运营金额' },
          tech_talent_amount: { type: :decimal, description: '人才培养金额' },
          group_assign_amount: { type: :decimal, description: '分配金额' },
          paid_unit: { type: :string, description: '支付单位' },
          paid_account: { type: :string, description: '支付账户' },
          paid_bank: { type: :string, description: '支付银行' },
          paid_date: { type: :date, description: '支付日期' },
          paid_num: { type: :string, description: '支付流水号' },
          paid_ticket_date: { type: :string, description: '开票时间' },
        }
      }
    }
  }
  fund_value = FactoryBot.attributes_for(:inno_fund)

  before :each do
  end

  path '/inno/user/funds' do

    get(summary: 'list funds') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_fund_count
        }
      end
    end

    post(summary: 'create fund') do
      produces 'application/json'
      consumes 'application/json'
      parameter :fund, in: :body, schema: fund_ref
      response(201, description: 'successful') do
        let(:fund) do
          { fund: fund_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['project_id']).to eq fund_value[:project_id]
          expect(body['creator_id']).to eq @user.id
          expect(body['seq']).not_to be_nil
          expect(body['planned_at']).to eq fund_value[:planned_at]
          expect(body['is_planned']).not_to be_nil
          expect(body['name']).to eq fund_value[:name]
          expect(body['state']).to eq fund_value[:state]
          expect(body['amount']).to eq fund_value[:amount]
          expect(body['position']).to eq fund_value[:position]
          expect(body['payload']).to eq fund_value[:payload]
          expect(body['attachment']).to eq fund_value[:attachment]
          expect(body['received_amount']).to eq fund_value[:received_amount]
          expect(body['received_date']).to eq fund_value[:received_date]
          expect(body['cost_amount']).to eq fund_value[:cost_amount]
          expect(body['income_amount']).to eq fund_value[:income_amount]
          expect(body['profit_amount']).to eq fund_value[:profit_amount]
          expect(body['tech_operator_amount']).to eq fund_value[:tech_operator_amount]
          expect(body['tech_talent_amount']).to eq fund_value[:tech_talent_amount]
          expect(body['group_assign_amount']).to eq fund_value[:group_assign_amount]
          expect(body['paid_unit']).to eq fund_value[:paid_unit]
          expect(body['paid_account']).to eq fund_value[:paid_account]
          expect(body['paid_bank']).to eq fund_value[:paid_bank]
          expect(body['paid_date']).to eq fund_value[:paid_date]
          expect(body['paid_num']).to eq fund_value[:paid_num]
          expect(body['paid_ticket_date']).to eq fund_value[:paid_ticket_date]
        }
      end
    end
  end

  path '/inno/user/funds/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show fund') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_funds.first.id }
        it {
          body = JSON.parse(response.body)
          fund = @inno_funds.first
          expect(body['app_id']).to eq fund.app_id
          expect(body['project_id']).to eq fund.project_id
          expect(body['creator_id']).to eq fund.creator_id
          expect(body['seq']).to eq fund.seq
          expect(body['planned_at']).to eq fund.planned_at
          expect(body['is_planned']).to eq fund.is_planned
          expect(body['name']).to eq fund.name
          expect(body['state']).to eq fund.state
          expect(body['amount']).to eq fund.amount
          expect(body['position']).to eq fund.position
          expect(body['payload']).to eq fund.payload
          expect(body['attachment']).to eq fund.attachment
          expect(body['received_amount']).to eq fund.received_amount
          expect(body['received_date']).to eq fund.received_date
          expect(body['cost_amount']).to eq fund.cost_amount
          expect(body['income_amount']).to eq fund.income_amount
          expect(body['profit_amount']).to eq fund.profit_amount
          expect(body['tech_operator_amount']).to eq fund.tech_operator_amount
          expect(body['tech_talent_amount']).to eq fund.tech_talent_amount
          expect(body['group_assign_amount']).to eq fund.group_assign_amount
          expect(body['paid_unit']).to eq fund.paid_unit
          expect(body['paid_account']).to eq fund.paid_account
          expect(body['paid_bank']).to eq fund.paid_bank
          expect(body['paid_date']).to eq fund.paid_date
          expect(body['paid_num']).to eq fund.paid_num
          expect(body['paid_ticket_date']).to eq fund.paid_ticket_date
        }
      end
    end

    patch(summary: 'update fund') do
      produces 'application/json'
      consumes 'application/json'
      parameter :fund, in: :body, schema: fund_ref
      response(201, description: 'successful') do
        let(:id) { @inno_funds.first.id }
        let(:fund) do
          { fund: fund_value }
        end
      end
    end

    delete(summary: 'delete fund') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_funds.first.id }
        it {
          expect(Inno::Fund.count).to eq(@inno_fund_count-1)
        }
      end
    end
  end
end

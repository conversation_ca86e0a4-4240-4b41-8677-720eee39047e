require 'swagger_helper'

RSpec.describe 'inno/user/works', type: :request, capture_examples: true, tags: ["inno user"] do
  work_ref = {
    type: :object, properties: {
      work: {
        type: :object, properties: {
          name: { type: :string, description: '作品名称' },
          serial_no: { type: :string, description: '流水号' },
          category: { type: :string, description: '作品类别' },
          maker: { type: :string, description: '制片者' },
          owner: { type: :string, description: '著作权人' },
          completion_date: { type: :date, description: '创作完成日期' },
          pub_date: { type: :date, description: '首次公映日期' },
          agency: { type: :string, description: '代理机构' },
          reg_date: { type: :date, description: '登记日期' },
          reg_no: { type: :string, description: '登记号' },
        }
      }
    }
  }
  work_value = FactoryBot.attributes_for(:inno_work)

  before :each do
  end

  path '/inno/user/works' do

    get(summary: 'list works') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_work_count
        }
      end
    end

    post(summary: 'create work') do
      produces 'application/json'
      consumes 'application/json'
      parameter :work, in: :body, schema: work_ref
      response(201, description: 'successful') do
        let(:work) do
          { work: work_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['name']).to eq work_value['name']
          expect(body['serial_no']).to eq work_value[:serial_no]
          expect(body['category']).to eq work_value[:category]
          expect(body['maker']).to eq work_value[:maker]
          expect(body['owner']).to eq work_value[:owner]
          expect(body['completion_date']).to eq work_value[:completion_date]
          expect(body['pub_date']).to eq work_value[:pub_date]
          expect(body['agency']).to eq work_value[:agency]
          expect(body['reg_date']).to eq work_value[:reg_date]
          expect(body['reg_no']).to eq work_value[:reg_no]
        }
      end
    end
  end

  path '/inno/user/works/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show work') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @inno_works.first.id }
        it {
          body = JSON.parse(response.body)
          work = @inno_works.first
          expect(body['name']).to eq work.name
          expect(body['serial_no']).to eq work.serial_no
          expect(body['category']).to eq work.category
          expect(body['maker']).to eq work.maker
          expect(body['owner']).to eq work.owner
          expect(body['completion_date']).to eq work.completion_date
          expect(body['pub_date']).to eq work.pub_date
          expect(body['agency']).to eq work.agency
          expect(body['reg_date']).to eq work.reg_date
          expect(body['reg_no']).to eq work.reg_no
        }
      end
    end

    patch(summary: 'update work') do
      produces 'application/json'
      consumes 'application/json'
      parameter :work, in: :body, schema: work_ref
      response(201, description: 'successful') do
        let(:id) { @inno_works.first.id }
        let(:work) do
          { work: work_value }
        end
      end
    end

    delete(summary: 'delete work') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @inno_works.first.id }
        it {
          expect(Inno::Work.count).to eq(@inno_work_count-1)
        }
      end
    end
  end
end

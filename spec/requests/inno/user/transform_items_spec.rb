require 'swagger_helper'

RSpec.describe 'inno/user/transform_items', type: :request, capture_examples: true, tags: ["inno user"] do
  transform_item_ref = {
    type: :object, properties: {
      transform_item: {
        type: :object, properties: {
          actual_amount: { type: :decimal, description: '实到金额' },
          payload: { type: :jsonb, description: '额外信息' },
        }
      }
    }
  }
  transform_item_value = FactoryBot.attributes_for(:inno_transform_item)

  before :each do
  end

  path '/inno/user/transforms/{transform_id}/transform_items' do
    parameter 'transform_id', in: :path, type: :string

    get(summary: 'list transform_items') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:transform_id) { @inno_transform.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @inno_transform_item_count
        }
      end
    end

    post(summary: 'create transform_item') do
      produces 'application/json'
      consumes 'application/json'
      parameter :transform_item, in: :body, schema: transform_item_ref
      response(201, description: 'successful') do
        let(:transform_id) { @inno_transform.id }
        let(:transform_item) do
          { transform_item: transform_item_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['actual_amount']).to eq transform_item_value['actual_amount'].to_s
          expect(body['payload']).to eq transform_item_value[:payload]
        }
      end
    end
  end


  path '/inno/user/transforms/{transform_id}/transform_items/{id}' do
    parameter 'transform_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show transform_item') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:transform_id) { @inno_transform.id }
        let(:id) { @inno_transform_items.first.id }
        it {
          body = JSON.parse(response.body)
          transform_item = @inno_transform_items.first
          expect(body['transform_id']).to eq transform_item.transform_id
          expect(body['source_type']).to eq transform_item.source_type
          expect(body['source_id']).to eq transform_item.source_id
          expect(body['creator_id']).to eq transform_item.creator_id
          expect(body['actual_amount']).to eq transform_item.actual_amount.to_s
          expect(body['payload']).to eq transform_item.payload
        }
      end
    end
  end
end

# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_07_05_061015) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
  t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_actions_on_app_id"
    t.index ["real_user_id"], name: "index_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_actions_on_user"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "blob_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "idx_on_record_type_record_id_name_blob_id_0be5805727", unique: true
    t.index ["record_type", "record_id"], name: "index_active_storage_attachments_on_record"
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.jsonb "metadata", comment: "额外信息"
    t.string "app_code", comment: "app标识"
    t.string "key", comment: "key"
    t.string "filename", comment: "文件名称"
    t.string "content_type", comment: "文件类型"
    t.string "service_name", comment: "服务名称"
    t.integer "byte_size", comment: "文件大小"
    t.string "checksum", comment: "校验位"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "api_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.bigint "app_id"
    t.string "klass", comment: "对应的active record class name"
    t.string "action", comment: "对应controller的action"
    t.string "uid", comment: "自动生成的唯一标识"
    t.jsonb "extract_conf", comment: "数据抽取配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_api_settings_on_app_id"
    t.index ["model_define_id"], name: "index_api_settings_on_model_define_id"
  end

  create_table "apps", force: :cascade do |t|
    t.string "code", comment: "应用标识"
    t.string "name", comment: "应用的名称"
    t.jsonb "settings", comment: "配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "async_tasks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "taskable_type"
    t.bigint "taskable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.string "flag", comment: "程序使用参数，唯一标识，前端配合使用"
    t.string "name", comment: "任务名称"
    t.integer "progress", comment: "进度(取整数)"
    t.string "state"
    t.string "perform_args", comment: "执行参数"
    t.jsonb "options", comment: "启动执行参数"
    t.jsonb "payload", comment: "处理信息"
    t.jsonb "result", comment: "异步处理的结果信息"
    t.jsonb "meta", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_async_tasks_on_app_id"
    t.index ["taskable_type", "taskable_id"], name: "index_async_tasks_on_taskable"
    t.index ["user_id"], name: "index_async_tasks_on_user_id"
  end

  create_table "bpm_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "icon", comment: "图标"
    t.integer "position", comment: "排序"
    t.boolean "published", comment: "是否发布"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_catalogs_on_app_id"
  end

  create_table "bpm_instance_relations", force: :cascade do |t|
    t.bigint "instance_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "随机数"
    t.string "model_setting_flag", comment: "对应模型的flag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["instance_id"], name: "index_bpm_instance_relations_on_instance_id"
    t.index ["source_type", "source_id"], name: "index_bpm_instance_relations_on_source"
  end

  create_table "bpm_instances", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "creator_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI"
    t.jsonb "payload", comment: "流程表单"
    t.jsonb "storage", comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
    t.jsonb "summary", comment: "instance在列表页显示的内容"
    t.string "state", comment: "流程状态"
    t.string "flowable_flag", comment: "flowable不同流程的flag"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "cache_payload", comment: "额外存储的结构，根据场合可以作为payload的存储"
    t.datetime "action_at", comment: "激活时间"
    t.jsonb "last_token_attr", comment: "最新token信息"
    t.string "level", comment: "流程级别"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_instances_on_app_id"
    t.index ["creator_id"], name: "index_bpm_instances_on_creator_id"
    t.index ["flowable_type", "flowable_id"], name: "index_bpm_instances_on_flowable"
    t.index ["workflow_id"], name: "index_bpm_instances_on_workflow_id"
  end

  create_table "bpm_place_relations", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_bpm_place_relations_on_source_id"
    t.index ["target_id"], name: "index_bpm_place_relations_on_target_id"
    t.index ["workflow_id"], name: "index_bpm_place_relations_on_workflow_id"
  end

  create_table "bpm_places", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "type", comment: "STI"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "desc", comment: "节点描述"
    t.integer "position", comment: "根据 tree 边生成的 position"
    t.boolean "is_summary", comment: "是否快捷引用"
    t.jsonb "fields", comment: "workflow form字段在这个place的权限，读写/可见"
    t.jsonb "place_form", comment: "动态表单的json字段"
    t.jsonb "options", comment: "节点的配置信息"
    t.jsonb "timer_options", comment: "节点时间配置"
    t.jsonb "trigger_options", comment: "token进出节点时候可能需要额外操作的内容"
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "layout_options", comment: "前端页面使用的配置"
    t.jsonb "activate_options", comment: "同步回调配置"
    t.jsonb "token_source_options", comment: "token place相关配置"
    t.jsonb "form_setting", comment: "表单配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_places_on_workflow_id"
  end

  create_table "bpm_rules", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "name", comment: "规则名称"
    t.integer "time_in_second", comment: "设定时间范围"
    t.string "type", comment: "STI"
    t.jsonb "options", comment: "具体配置内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_rules_on_workflow_id"
  end

  create_table "bpm_stars", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "workflow_id"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bpm_stars_on_user_id"
    t.index ["workflow_id"], name: "index_bpm_stars_on_workflow_id"
  end

  create_table "bpm_tokens", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "bpm_transitions", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "place_id"
    t.string "type", comment: "STI"
    t.jsonb "callback_options", comment: "回调设置"
    t.jsonb "options", comment: "transition跳转的额外设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["place_id"], name: "index_bpm_transitions_on_place_id"
    t.index ["workflow_id"], name: "index_bpm_transitions_on_workflow_id"
  end

  create_table "bpm_workflow_relations", force: :cascade do |t|
    t.string "workflowable_type"
    t.bigint "workflowable_id"
    t.bigint "workflow_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_workflow_relations_on_workflow_id"
    t.index ["workflowable_type", "workflowable_id"], name: "index_bpm_workflow_relations_on_workflowable"
  end

  create_table "bpm_workflows", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "mod_id"
    t.bigint "catalog_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "流程名称"
    t.text "desc", comment: "流程描述"
    t.jsonb "icon", comment: "流程图标"
    t.jsonb "cover_image", comment: "流程封面"
    t.string "state"
    t.integer "position", comment: "catalog内排序"
    t.string "instance_type", comment: "自动生成的instance_type"
    t.string "classify"
    t.jsonb "form", comment: "表单配置 "
    t.jsonb "meta", comment: "工作流额外配置信息 "
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "trigger_options", comment: "instance状态改变时候需要额外操作的内容"
    t.boolean "auto_complete_same_handle_token", comment: "是否跳过连续相同的审批人"
    t.jsonb "submit_options", comment: "限制条件"
    t.jsonb "form_setting", comment: "表单配置"
    t.boolean "enable_level", comment: "是否启用 instance 优先级"
    t.jsonb "level_options", comment: "优先级配置"
    t.jsonb "conf", comment: "其他配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_workflows_on_app_id"
    t.index ["catalog_id"], name: "index_bpm_workflows_on_catalog_id"
    t.index ["creator_id"], name: "index_bpm_workflows_on_creator_id"
    t.index ["mod_id"], name: "index_bpm_workflows_on_mod_id"
  end

  create_table "com_private_policies", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "条款名称"
    t.string "key", comment: "关键字，可能有不同业务模块需要使用的关键字"
    t.jsonb "content", comment: "隐私条款内容"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_private_policies_on_app_id"
  end

  create_table "com_record_storages", force: :cascade do |t|
    t.bigint "user_id"
    t.string "key", comment: "缓存区key"
    t.jsonb "storage", comment: "属性暂存区"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_com_record_storages_on_user_id"
  end

  create_table "com_search_items", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "搜索条件 "
    t.integer "position", comment: "位置"
    t.string "group_name", comment: "分组标识"
    t.boolean "enabled", comment: "是否启用"
    t.jsonb "conditions", comment: "具体ransack搜索条件"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_search_items_on_app_id"
  end

  create_table "com_themes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "主题名称"
    t.jsonb "conf", comment: "主题配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_themes_on_app_id"
  end

  create_table "com_version_histories", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "版本发布名称"
    t.string "version", comment: "版本号"
    t.jsonb "content", comment: "发布说明"
    t.integer "position", comment: "发布顺序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_version_histories_on_app_id"
    t.index ["creator_id"], name: "index_com_version_histories_on_creator_id"
  end

  create_table "component_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组件配置名称"
    t.string "component_klass", comment: "组件类名称"
    t.string "component_path", comment: "组件类路径"
    t.jsonb "conf", comment: "组件配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_component_settings_on_app_id"
  end

  create_table "data_counter_stats", force: :cascade do |t|
    t.string "countable_type"
    t.bigint "countable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.date "date", comment: "日期，如果是年、月则存第一天"
    t.integer "hour", comment: "小时"
    t.string "period"
    t.integer "view_count", comment: "浏览量"
    t.integer "action_count", comment: "使用量"
    t.integer "user_count", comment: "用户量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["countable_type", "countable_id"], name: "index_data_counter_stats_on_countable"
  end

  create_table "data_forms", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "create_user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "record_type"
    t.bigint "record_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "可用作同一 source 下不同的关联关系的区分"
    t.string "source_flag", comment: "关联source的flag"
    t.string "state", comment: "数据状态"
    t.jsonb "payload", comment: "存储的信息"
    t.jsonb "summary", comment: "通过form生成的缩略信息"
    t.jsonb "form_conf", comment: "表单的配置，里面支持多态的方式"
    t.jsonb "options", comment: "额外的数据信息"
    t.jsonb "meta", comment: "预留后续的数据存储"
    t.string "form_conf_seq", comment: "表单配置的seq，方便进行检索"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_forms_on_app_id"
    t.index ["create_user_id"], name: "index_data_forms_on_create_user_id"
    t.index ["record_type", "record_id"], name: "index_data_forms_on_record"
    t.index ["source_type", "source_id"], name: "index_data_forms_on_source"
  end

  create_table "data_scopes", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.jsonb "config", comment: "配置"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_scopes_on_app_id"
    t.index ["tanent_id"], name: "index_data_scopes_on_tanent_id"
    t.index ["user_id"], name: "index_data_scopes_on_user_id"
  end

  create_table "data_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "op", comment: "操作"
    t.jsonb "infos", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_transfers_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_transfers_on_source"
    t.index ["target_type", "target_id"], name: "index_data_transfers_on_target"
  end

  create_table "data_view_logs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "view_at", comment: "最新访问时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_view_logs_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_view_logs_on_source"
    t.index ["user_id"], name: "index_data_view_logs_on_user_id"
  end

  create_table "department_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "department_anc_desc_idx", unique: true
  end

  create_table "department_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "部门类型名称"
    t.string "department_type", comment: "Department的类型，可能会关系到Department的STI"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_department_identities_on_app_id"
  end

  create_table "departments", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "root_org_id"
    t.bigint "department_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_identity_id"], name: "index_departments_on_department_identity_id"
    t.index ["org_id"], name: "index_departments_on_org_id"
    t.index ["root_org_id"], name: "index_departments_on_root_org_id"
  end

  create_table "disk_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "views", comment: "权限设置"
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "资源名称"
    t.text "desc", comment: "资源描述"
    t.string "type", comment: "STI"
    t.string "ftype", comment: "文件类型"
    t.jsonb "attachment", comment: "文件地址"
    t.boolean "read_only"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_disk_items_on_app_id"
    t.index ["creator_id"], name: "index_disk_items_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_disk_items_on_source"
  end

  create_table "disk_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "disk_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_disk_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_disk_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "disk_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_disk_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "disk_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_disk_permit_actions_on_user"
  end

  create_table "disk_recent_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "source_id"
    t.bigint "item_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_disk_recent_items_on_app_id"
    t.index ["creator_id"], name: "index_disk_recent_items_on_creator_id"
    t.index ["item_id"], name: "index_disk_recent_items_on_item_id"
    t.index ["source_id"], name: "index_disk_recent_items_on_source_id"
  end

  create_table "disk_sources", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "target_type"
    t.bigint "target_id"
    t.jsonb "views", comment: "权限设置"
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.text "desc", comment: "描述"
    t.boolean "shared", comment: "是否开放"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_disk_sources_on_app_id"
    t.index ["creator_id"], name: "index_disk_sources_on_creator_id"
    t.index ["target_type", "target_id"], name: "index_disk_sources_on_target"
  end

  create_table "disk_tag_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "disk_tag_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_disk_tag_actions_on_app_id"
    t.index ["real_user_id"], name: "index_disk_tag_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "disk_tag_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_disk_tag_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "disk_tag_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_disk_tag_actions_on_user"
  end

  create_table "disk_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.string "type", comment: "STI类型"
    t.string "name", comment: "标签名称"
    t.integer "position", comment: "排序"
    t.boolean "recommend", comment: "是否推荐"
    t.string "color", comment: "标签颜色设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_disk_tags_on_app_id"
    t.index ["source_type", "source_id"], name: "index_disk_tags_on_source"
  end

  create_table "duties", force: :cascade do |t|
    t.bigint "duty_group_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.string "name", comment: "职务名称"
    t.string "rank", comment: "职务等级"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_duties_on_department_id"
    t.index ["duty_group_id"], name: "index_duties_on_duty_group_id"
    t.index ["org_id"], name: "index_duties_on_org_id"
  end

  create_table "duty_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "角色组名称"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位组标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_duty_groups_on_app_id"
    t.index ["org_id"], name: "index_duty_groups_on_org_id"
  end

  create_table "favor_folders", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "收藏夹名称"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "content", comment: "详情"
    t.integer "position", comment: "排序"
    t.jsonb "option", comment: "额外配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_favor_folders_on_app_id"
    t.index ["user_id"], name: "index_favor_folders_on_user_id"
  end

  create_table "favor_mark_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "favor_mark_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_favor_mark_actions_on_app_id"
    t.index ["real_user_id"], name: "index_favor_mark_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "favor_mark_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_favor_mark_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "favor_mark_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_favor_mark_actions_on_user"
  end

  create_table "forms_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.string "uuid", comment: "表单的唯一标识，可以替代id给前端使用"
    t.string "name", comment: "表单的名称"
    t.jsonb "form", comment: "表单配置的内容"
    t.jsonb "form_setting", comment: "表单内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_forms_templates_on_app_id"
  end

  create_table "grant_applications", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "应用名称"
    t.string "type", comment: "STI类型"
    t.string "app_key", comment: "app key"
    t.string "app_secret", comment: "app secret"
    t.jsonb "options", comment: "额外的配置, { encrypt: sm 或 aes }"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_grant_applications_on_app_id"
  end

  create_table "inno_awards", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "name", comment: "获奖名称"
    t.integer "year", comment: "年份"
    t.string "grant_unit", comment: "授奖单位"
    t.string "level", comment: "奖励级别"
    t.string "completer", comment: "完成人"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", comment: "排序"
    t.index ["app_id"], name: "index_inno_awards_on_app_id"
    t.index ["creator_id"], name: "index_inno_awards_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_awards_on_source"
  end

  create_table "inno_competitive_products", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "name", comment: "名称"
    t.string "company_name", comment: "生产企业"
    t.string "approval_no", comment: "批准文号"
    t.text "intro", comment: "简介"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.index ["app_id"], name: "index_inno_competitive_products_on_app_id"
    t.index ["creator_id"], name: "index_inno_competitive_products_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_competitive_products_on_source"
  end

  create_table "inno_department_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "inno_department_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_inno_department_actions_on_app_id"
    t.index ["real_user_id"], name: "index_inno_department_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "inno_department_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_inno_department_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "inno_department_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_inno_department_actions_on_user"
  end

  create_table "inno_funds", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "project_id"
    t.bigint "creator_id"
    t.string "seq", comment: "编号"
    t.datetime "planned_at"
    t.boolean "is_planned"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.decimal "amount", comment: "金额"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.jsonb "attachment", comment: "附件"
    t.decimal "received_amount", comment: "到账金额"
    t.date "received_date", comment: "收款日期"
    t.decimal "cost_amount", comment: "成本"
    t.decimal "income_amount", comment: "收入"
    t.decimal "profit_amount", comment: "收益"
    t.decimal "tech_operator_amount", comment: "运营金额"
    t.decimal "tech_talent_amount", comment: "人才培养金额"
    t.decimal "group_assign_amount", comment: "分配金额"
    t.string "paid_unit", comment: "支付单位"
    t.string "paid_account", comment: "支付账户"
    t.string "paid_bank", comment: "支付银行"
    t.date "paid_date", comment: "支付日期"
    t.string "paid_num", comment: "支付流水号"
    t.string "paid_ticket_date", comment: "开票时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "group_user_count", comment: "项目组分配人数"
    t.jsonb "meta", comment: "配置字段"
    t.index ["app_id"], name: "index_inno_funds_on_app_id"
    t.index ["creator_id"], name: "index_inno_funds_on_creator_id"
    t.index ["project_id"], name: "index_inno_funds_on_project_id"
  end

  create_table "inno_other_ips", force: :cascade do |t|
    t.bigint "creator_id"
    t.bigint "app_id"
    t.integer "transforms_count", comment: "转化次数"
    t.string "transform_state", comment: "转化状态"
    t.integer "stars_count"
    t.string "name", comment: "名称"
    t.string "kind", comment: "类型"
    t.date "grant_date", comment: "授权日"
    t.date "apply_date", comment: "申请日"
    t.string "apply_no", comment: "申请号"
    t.string "cert_no", comment: "证书号"
    t.string "obligee", comment: "权利人"
    t.string "inventor", comment: "发明人/设计人"
    t.string "agency", comment: "代理机构"
    t.string "contact_name", comment: "联系人"
    t.string "contact_mobile", comment: "联系方式"
    t.string "convert_intention", comment: "转化意向"
    t.string "convert_way", comment: "转化方式"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_inno_other_ips_on_app_id"
    t.index ["creator_id"], name: "index_inno_other_ips_on_creator_id"
  end

  create_table "inno_ownerships", force: :cascade do |t|
    t.bigint "app_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "user_id"
    t.string "role", comment: "角色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "department_id"
    t.index ["app_id"], name: "index_inno_ownerships_on_app_id"
    t.index ["department_id"], name: "index_inno_ownerships_on_department_id"
    t.index ["resource_type", "resource_id"], name: "index_inno_ownerships_on_resource"
  end

  create_table "inno_papers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "name", comment: "论文名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "author", comment: "作者"
    t.string "journal_name", comment: "期刊名称"
    t.string "jcr", comment: "JCR分区"
    t.integer "year", comment: "发表年份"
    t.string "pmid", comment: "PMID"
    t.text "intro", comment: "简介"
    t.integer "position", comment: "排序位置"
    t.index ["app_id"], name: "index_inno_papers_on_app_id"
    t.index ["creator_id"], name: "index_inno_papers_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_papers_on_source"
  end

  create_table "inno_patents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "专利名称"
    t.string "apply_no", comment: "专利申请号"
    t.date "apply_date", comment: "申请日期"
    t.date "pub_date", comment: "公开/公告日期"
    t.text "abstract", comment: "专利摘要"
    t.string "ipc", comment: "IPC分类号"
    t.string "patent_type", comment: "专利类型"
    t.string "legal_state", comment: "法律状态"
    t.string "transform_state", comment: "转化状态"
    t.jsonb "payload", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "transforms_count", comment: "转化次数"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.integer "stars_count"
    t.date "expire_date", comment: "失效日期"
    t.string "ip_state"
    t.bigint "creator_id"
    t.string "patentee", comment: "专利权人"
    t.string "inventors", comment: "发明人"
    t.string "contact_name", comment: "联系人"
    t.string "contact_mobile", comment: "联系电话"
    t.string "agency", comment: "代理机构"
    t.string "agency_code", comment: "代理机构代码"
    t.string "patentee_state", comment: "专利权人状态"
    t.string "state", comment: "专利权状态"
    t.string "convert_intention", comment: "转化意向"
    t.string "convert_way", comment: "转化意向"
    t.string "pub_no", comment: "公开号"
    t.date "payment_start_date", comment: "生效时间"
    t.date "payment_end_date", comment: "失效时间"
    t.boolean "payment", comment: "专利是否需要付费使用"
    t.index ["app_id"], name: "index_inno_patents_on_app_id"
    t.index ["creator_id"], name: "index_inno_patents_on_creator_id"
  end

  create_table "inno_payments", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "patent_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "seq", comment: "编号"
    t.date "start_date", comment: "生效时间"
    t.date "end_date", comment: "失效时间"
    t.string "name", comment: "名称"
    t.string "state"
    t.decimal "amount", comment: "金额"
    t.datetime "paid_at", comment: "支付时间"
    t.string "paid_type", comment: "支付方式"
    t.jsonb "attachment", comment: "附件"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_inno_payments_on_app_id"
    t.index ["patent_id"], name: "index_inno_payments_on_patent_id"
    t.index ["source_type", "source_id"], name: "index_inno_payments_on_source"
    t.index ["user_id"], name: "index_inno_payments_on_user_id"
  end

  create_table "inno_project_relations", force: :cascade do |t|
    t.bigint "project_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["project_id"], name: "index_inno_project_relations_on_project_id"
    t.index ["source_type", "source_id"], name: "index_inno_project_relations_on_source"
  end

  create_table "inno_projects", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "name", comment: "项目名称"
    t.string "leader_name", comment: "负责人姓名"
    t.decimal "contract_amount", comment: "合同金额"
    t.decimal "actual_amount", comment: "实到金额"
    t.jsonb "payload", comment: "信息"
    t.string "state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_at", comment: "开始时间"
    t.datetime "end_at", comment: "合同结束时间"
    t.index ["app_id"], name: "index_inno_projects_on_app_id"
    t.index ["creator_id"], name: "index_inno_projects_on_creator_id"
  end

  create_table "inno_scientific_researches", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "name", comment: "课题名称"
    t.string "num", comment: "课题编号"
    t.string "leader", comment: "项目负责人"
    t.string "undertaking_unit", comment: "承担单位"
    t.string "level", comment: "项目级别"
    t.string "from", comment: "项目来源"
    t.string "kind", comment: "项目类型"
    t.date "init_at", comment: "立项时间"
    t.date "start_at", comment: "开始时间"
    t.date "end_at", comment: "结束时间"
    t.decimal "funds", comment: "项目经费"
    t.string "financial_support", comment: "财政课题资助"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", comment: "排序"
    t.index ["app_id"], name: "index_inno_scientific_researches_on_app_id"
    t.index ["creator_id"], name: "index_inno_scientific_researches_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_scientific_researches_on_source"
  end

  create_table "inno_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "名称"
    t.string "state"
    t.integer "position", comment: "排序"
    t.jsonb "option", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_inno_settings_on_app_id"
  end

  create_table "inno_software_copyrights", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "软件名称"
    t.string "serial_no", comment: "流水号"
    t.string "reg_no", comment: "登记号"
    t.string "owner", comment: "著作权人"
    t.string "right_way", comment: "权利取得方式"
    t.string "right_range", comment: "权利防伪"
    t.string "cert_no", comment: "证书号"
    t.string "ip_state"
    t.string "transform_state", comment: "转化状态"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "transforms_count", comment: "转化次数"
    t.integer "stars_count"
    t.bigint "creator_id"
    t.string "short_name", comment: "软件简称"
    t.string "version_no", comment: "版本号"
    t.date "grant_date", comment: "授权日"
    t.string "apply_identity", comment: "办理身份"
    t.string "member_names", comment: "团队成员"
    t.date "apply_date", comment: "申请日"
    t.string "apply_way", comment: "申请方式"
    t.string "soft_kind", comment: "软件分类"
    t.date "complete_date", comment: "开发完成日期"
    t.string "pub_state", comment: "发表状态"
    t.string "contact_name", comment: "联系人"
    t.string "contact_mobile", comment: "联系方式"
    t.string "convert_intention", comment: "转化意向"
    t.string "convert_way", comment: "转化方式"
    t.jsonb "payload"
    t.index ["app_id"], name: "index_inno_software_copyrights_on_app_id"
    t.index ["creator_id"], name: "index_inno_software_copyrights_on_creator_id"
  end

  create_table "inno_tag_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "inno_tag_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_inno_tag_actions_on_app_id"
    t.index ["real_user_id"], name: "index_inno_tag_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "inno_tag_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_inno_tag_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "inno_tag_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_inno_tag_actions_on_user"
  end

  create_table "inno_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.string "type", comment: "STI类型"
    t.string "name", comment: "标签名称"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "color", comment: "标签颜色设置"
    t.index ["app_id"], name: "index_inno_tags_on_app_id"
  end

  create_table "inno_team_members", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "user_id"
    t.bigint "department_id"
    t.bigint "creator_id"
    t.string "role", comment: "团队角色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", comment: "排序"
    t.index ["app_id"], name: "index_inno_team_members_on_app_id"
    t.index ["creator_id"], name: "index_inno_team_members_on_creator_id"
    t.index ["department_id"], name: "index_inno_team_members_on_department_id"
    t.index ["source_type", "source_id"], name: "index_inno_team_members_on_source"
    t.index ["user_id"], name: "index_inno_team_members_on_user_id"
  end

  create_table "inno_transform_items", force: :cascade do |t|
    t.bigint "transform_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "creator_id"
    t.decimal "actual_amount", comment: "实到金额"
    t.jsonb "payload", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["creator_id"], name: "index_inno_transform_items_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_transform_items_on_source"
    t.index ["transform_id"], name: "index_inno_transform_items_on_transform_id"
  end

  create_table "inno_transforms", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "state", comment: "状态属性"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "contract_amount", comment: "合同金额"
    t.decimal "actual_amount", comment: "实到金额"
    t.jsonb "payload", comment: "额外信息"
    t.bigint "creator_id"
    t.index ["app_id"], name: "index_inno_transforms_on_app_id"
    t.index ["creator_id"], name: "index_inno_transforms_on_creator_id"
    t.index ["source_type", "source_id"], name: "index_inno_transforms_on_source"
  end

  create_table "inno_works", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "作品名称"
    t.string "serial_no", comment: "流水号"
    t.string "category", comment: "作品类别"
    t.string "maker", comment: "制片者"
    t.string "owner", comment: "著作权人"
    t.date "completion_date", comment: "创作完成日期"
    t.date "pub_date", comment: "首次公映日期"
    t.string "agency", comment: "代理机构"
    t.date "reg_date", comment: "登记日期"
    t.string "reg_no", comment: "登记号"
    t.string "ip_state"
    t.string "transform_state", comment: "转化状态"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "transforms_count", comment: "转化次数"
    t.integer "stars_count"
    t.bigint "creator_id"
    t.date "apply_date", comment: "申请日期"
    t.string "apply_identity", comment: "办理身份"
    t.string "obtain_rights_way", comment: "权利取得方式"
    t.string "rights_belong_way", comment: "权利归属方式"
    t.string "contact_name", comment: "联系人"
    t.string "contact_mobile", comment: "联系电话"
    t.string "convert_intention", comment: "转化意向"
    t.string "convert_way", comment: "转化意向"
    t.string "pub_state", comment: "发表状态"
    t.jsonb "payload", comment: "额外信息"
    t.index ["app_id"], name: "index_inno_works_on_app_id"
    t.index ["creator_id"], name: "index_inno_works_on_creator_id"
  end

  create_table "member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.string "name", comment: "身份名称"
    t.string "member_type", comment: "Member的类型"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.jsonb "form", comment: "Member配置的表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_member_identities_on_app_id"
    t.index ["org_id"], name: "index_member_identities_on_org_id"
  end

  create_table "member_identity_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "member_identity_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_member_identity_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_member_identity_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "member_identity_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_member_identity_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "member_identity_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_member_identity_permit_actions_on_user"
  end

  create_table "member_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "请求名称"
    t.jsonb "payload", comment: "相关信息，会存储到member的payload里"
    t.jsonb "member_attributes", comment: "相关信息，会存储到member的attributes里"
    t.jsonb "options", comment: "加入什么组织和岗位，相关配置"
    t.string "state", comment: "状态"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.bigint "member_id"
    t.string "type", comment: "STI"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_member_requests_on_app_id"
    t.index ["member_id"], name: "index_member_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_member_requests_on_member_identity_id"
    t.index ["tanent_id"], name: "index_member_requests_on_tanent_id"
    t.index ["user_id"], name: "index_member_requests_on_user_id"
  end

  create_table "members", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "app_id"
    t.bigint "member_request_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.string "code", comment: "用户标识"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.index ["app_id"], name: "index_members_on_app_id"
    t.index ["member_identity_id"], name: "index_members_on_member_identity_id"
    t.index ["member_request_id"], name: "index_members_on_member_request_id"
    t.index ["tanent_id"], name: "index_members_on_tanent_id"
    t.index ["user_id"], name: "index_members_on_user_id"
  end

  create_table "memberships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", comment: "生效时间，可以为空"
    t.datetime "invalid_at", comment: "失效时间，可以为空"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "duty_rank"
    t.boolean "priority", comment: "是否主要岗位"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_memberships_on_app_id"
    t.index ["department_id"], name: "index_memberships_on_department_id"
    t.index ["duty_id"], name: "index_memberships_on_duty_id"
    t.index ["member_id"], name: "index_memberships_on_member_id"
    t.index ["org_id"], name: "index_memberships_on_org_id"
    t.index ["user_id"], name: "index_memberships_on_user_id"
  end

  create_table "model_confs", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "name", comment: "名称"
    t.string "klass", comment: "类名"
    t.jsonb "conf", comment: "具体配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model_define_id"], name: "index_model_confs_on_model_define_id"
  end

  create_table "model_defines", force: :cascade do |t|
    t.string "klass", comment: "对应设置的Model名称"
    t.string "name", comment: "模型设置的中文名"
    t.string "association_chain"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "model_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "setable_type"
    t.bigint "setable_id"
    t.bigint "app_id"
    t.bigint "forms_template_id"
    t.string "flag", comment: "同一个模型中的不同定义，其中model代表是这个对象的模型"
    t.string "flag_name", comment: "flag对应中文名称"
    t.jsonb "form", comment: "可以直接定义表单"
    t.jsonb "form_setting", comment: "表单结构"
    t.jsonb "api_config", comment: "API Config"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "bpm_workflow_id"
    t.bigint "ref_model_setting_id"
    t.string "ref_model_setting_flag", comment: "关联model_setting_flag"
    t.index ["app_id"], name: "index_model_settings_on_app_id"
    t.index ["bpm_workflow_id"], name: "index_model_settings_on_bpm_workflow_id"
    t.index ["forms_template_id"], name: "index_model_settings_on_forms_template_id"
    t.index ["model_define_id"], name: "index_model_settings_on_model_define_id"
    t.index ["ref_model_setting_id"], name: "index_model_settings_on_ref_model_setting_id"
    t.index ["setable_type", "setable_id"], name: "index_model_settings_on_setable"
  end

  create_table "mods", force: :cascade do |t|
    t.string "name", comment: "模块名称"
    t.string "key", comment: "模块对应查找的key值"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "opm_balances", force: :cascade do |t|
    t.bigint "user_id"
    t.string "category"
    t.decimal "total"
    t.datetime "expires_at"
    t.string "reason"
    t.jsonb "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_opm_balances_on_user_id"
  end

  create_table "opm_employ_invites", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "org_id"
    t.bigint "member_identity_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_employ_invites_on_app_id"
    t.index ["creator_id"], name: "index_opm_employ_invites_on_creator_id"
    t.index ["department_id"], name: "index_opm_employ_invites_on_department_id"
    t.index ["duty_id"], name: "index_opm_employ_invites_on_duty_id"
    t.index ["member_identity_id"], name: "index_opm_employ_invites_on_member_identity_id"
    t.index ["org_id"], name: "index_opm_employ_invites_on_org_id"
  end

  create_table "opm_former_employees", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.bigint "user_id"
    t.date "leave_at", comment: "离职时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_former_employees_on_app_id"
    t.index ["member_id"], name: "index_opm_former_employees_on_member_id"
    t.index ["org_id"], name: "index_opm_former_employees_on_org_id"
    t.index ["user_id"], name: "index_opm_former_employees_on_user_id"
  end

  create_table "opm_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "编号"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_groups_on_app_id"
  end

  create_table "opm_holiday_travel_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.datetime "start_at", comment: "开始时间"
    t.datetime "end_at", comment: "结束时间"
    t.decimal "duration", comment: "时长"
    t.text "reason", comment: "理由"
    t.jsonb "attachment", comment: "附件"
    t.string "state"
    t.boolean "is_workday", comment: "是否工作日"
    t.string "destination", comment: "目的地"
    t.string "contact_info", comment: "联系方式"
    t.string "report_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_holiday_travel_requests_on_app_id"
    t.index ["user_id"], name: "index_opm_holiday_travel_requests_on_user_id"
  end

  create_table "opm_hr_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "ownership_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "creator_id"
    t.bigint "employ_invite_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.jsonb "payload", comment: "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_hr_requests_on_app_id"
    t.index ["creator_id"], name: "index_opm_hr_requests_on_creator_id"
    t.index ["employ_invite_id"], name: "index_opm_hr_requests_on_employ_invite_id"
    t.index ["member_id"], name: "index_opm_hr_requests_on_member_id"
    t.index ["org_id"], name: "index_opm_hr_requests_on_org_id"
    t.index ["ownership_id"], name: "index_opm_hr_requests_on_ownership_id"
    t.index ["user_id"], name: "index_opm_hr_requests_on_user_id"
  end

  create_table "opm_hr_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.bigint "user_id"
    t.string "type", comment: "STI类型"
    t.jsonb "payload", comment: "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_hr_transfers_on_app_id"
    t.index ["member_id"], name: "index_opm_hr_transfers_on_member_id"
    t.index ["org_id"], name: "index_opm_hr_transfers_on_org_id"
    t.index ["user_id"], name: "index_opm_hr_transfers_on_user_id"
  end

  create_table "opm_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "seq", comment: "编号"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "content", comment: "内容"
    t.string "origin", comment: "来源"
    t.string "mode", comment: "分类"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_items_on_app_id"
    t.index ["member_id"], name: "index_opm_items_on_member_id"
    t.index ["user_id"], name: "index_opm_items_on_user_id"
  end

  create_table "opm_job_titles", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "group_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "level", comment: "等级"
    t.string "code", comment: "编号"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_job_titles_on_app_id"
    t.index ["group_id"], name: "index_opm_job_titles_on_group_id"
  end

  create_table "opm_leave_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.string "type"
    t.datetime "start_at"
    t.datetime "end_at"
    t.decimal "duration"
    t.text "reason"
    t.jsonb "attachment"
    t.jsonb "model_payload"
    t.string "leave_type"
    t.string "unit_type"
    t.decimal "min_unit"
    t.decimal "max_unit"
    t.decimal "min_interval"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_leave_requests_on_app_id"
    t.index ["flowable_type", "flowable_id"], name: "index_opm_leave_requests_on_flowable"
    t.index ["user_id"], name: "index_opm_leave_requests_on_user_id"
  end

  create_table "opm_ownerships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.bigint "department_identity_id"
    t.bigint "member_identity_id"
    t.bigint "duty_id"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_ownerships_on_app_id"
    t.index ["department_id"], name: "index_opm_ownerships_on_department_id"
    t.index ["department_identity_id"], name: "index_opm_ownerships_on_department_identity_id"
    t.index ["duty_id"], name: "index_opm_ownerships_on_duty_id"
    t.index ["member_identity_id"], name: "index_opm_ownerships_on_member_identity_id"
    t.index ["org_id"], name: "index_opm_ownerships_on_org_id"
  end

  create_table "opm_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "opm_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_opm_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_opm_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "opm_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_opm_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "opm_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_opm_permit_actions_on_user"
  end

  create_table "opm_records", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "编号"
    t.string "origin", comment: "来源"
    t.string "level", comment: "级别"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_records_on_app_id"
    t.index ["member_id"], name: "index_opm_records_on_member_id"
    t.index ["source_type", "source_id"], name: "index_opm_records_on_source"
    t.index ["user_id"], name: "index_opm_records_on_user_id"
  end

  create_table "opm_relate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "opm_relate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_opm_relate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_opm_relate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "opm_relate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_opm_relate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "opm_relate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_opm_relate_actions_on_user"
  end

  create_table "opm_transactions", force: :cascade do |t|
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "category"
    t.decimal "amount"
    t.string "operation"
    t.datetime "expires_at"
    t.string "reason"
    t.jsonb "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_type", "source_id"], name: "index_opm_transactions_on_source"
    t.index ["user_id"], name: "index_opm_transactions_on_user_id"
  end

  create_table "org_clients", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "client_type"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_clients_on_app_id"
    t.index ["client_type", "client_id"], name: "index_org_clients_on_client"
    t.index ["org_id"], name: "index_org_clients_on_org_id"
  end

  create_table "org_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "org_anc_desc_idx", unique: true
  end

  create_table "org_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "组织类型名称"
    t.string "org_type", comment: "Org的类型，可能会关系到Org的STI"
    t.integer "orgs_count", comment: "关联的Org数量"
    t.jsonb "form", comment: "Member配置的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_identities_on_app_id"
  end

  create_table "org_member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "code", comment: "标识"
    t.string "org_member_type", comment: "OrgMember的类型"
    t.jsonb "settle_in_form", comment: "入驻申请表单"
    t.jsonb "postpone_form", comment: "延期申请表单"
    t.jsonb "form", comment: "表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_member_identities_on_app_id"
  end

  create_table "org_members", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.string "type", comment: "STI类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_org_members_on_app_id"
    t.index ["org_id"], name: "index_org_members_on_org_id"
    t.index ["org_member_identity_id"], name: "index_org_members_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_members_on_tanent_id"
  end

  create_table "org_ownerships", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["org_id"], name: "index_org_ownerships_on_org_id"
    t.index ["user_id"], name: "index_org_ownerships_on_user_id"
  end

  create_table "org_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "member_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.bigint "org_member_id"
    t.bigint "org_identity_id"
    t.bigint "tanent_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组织名称"
    t.string "code", comment: "组织标识"
    t.jsonb "org_payload", comment: "相关信息，会存储到org的payload里"
    t.jsonb "member_payload", comment: "相关信息，会存储到member的payload里"
    t.string "type", comment: "STI"
    t.string "state", comment: "状态: draft, approving"
    t.datetime "approval_at", comment: "审批通过时间"
    t.jsonb "options", comment: "其他预留信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_org_requests_on_app_id"
    t.index ["member_id"], name: "index_org_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_org_requests_on_member_identity_id"
    t.index ["org_id"], name: "index_org_requests_on_org_id"
    t.index ["org_identity_id"], name: "index_org_requests_on_org_identity_id"
    t.index ["org_member_id"], name: "index_org_requests_on_org_member_id"
    t.index ["org_member_identity_id"], name: "index_org_requests_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_requests_on_tanent_id"
    t.index ["user_id"], name: "index_org_requests_on_user_id"
  end

  create_table "orgs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_orgs_on_app_id"
    t.index ["org_identity_id"], name: "index_orgs_on_org_identity_id"
    t.index ["tanent_id"], name: "index_orgs_on_tanent_id"
  end

  create_table "page_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "页面配置名称"
    t.jsonb "conf", comment: "页面配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_page_settings_on_app_id"
  end

  create_table "paper_trail_versions", force: :cascade do |t|
    t.string "operator_type"
    t.bigint "operator_id"
    t.string "item_type"
    t.integer "item_id"
    t.string "event", comment: "create, update, destroy"
    t.string "whodunnit", comment: "whodunnit"
    t.jsonb "object", comment: "object attributes"
    t.jsonb "object_changes", comment: "object changes"
    t.jsonb "controller_info", comment: "controller info"
    t.jsonb "model_info", comment: "model info"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_type", "item_id"], name: "index_versions_on_item_id_item_type"
    t.index ["operator_type", "operator_id"], name: "index_paper_trail_versions_on_operator"
  end

  create_table "permit_permissions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.bigint "route_setting_id"
    t.string "platform", comment: "平台"
    t.string "aname", comment: "action名称"
    t.string "cname", comment: "controller名称"
    t.string "klass", comment: "controller"
    t.string "action", comment: "action"
    t.jsonb "whitelist"
    t.jsonb "blacklist"
    t.jsonb "payload"
    t.integer "position"
    t.string "key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_permit_permissions_on_app_id"
    t.index ["mod_id"], name: "index_permit_permissions_on_mod_id"
    t.index ["route_setting_id"], name: "index_permit_permissions_on_route_setting_id"
    t.index ["tanent_id"], name: "index_permit_permissions_on_tanent_id"
    t.index ["user_id"], name: "index_permit_permissions_on_user_id"
  end

  create_table "res_book_relations", force: :cascade do |t|
    t.bigint "book_id"
    t.string "source_type"
    t.bigint "source_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["book_id"], name: "index_res_book_relations_on_book_id"
    t.index ["source_type", "source_id"], name: "index_res_book_relations_on_source"
  end

  create_table "res_books", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "说明"
    t.string "relation_type", comment: "通讯录的类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_books_on_app_id"
    t.index ["user_id"], name: "index_res_books_on_user_id"
  end

  create_table "res_member_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_member_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_member_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_member_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_member_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_member_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_member_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_member_actions_on_user"
  end

  create_table "res_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_permit_actions_on_user"
  end

  create_table "res_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "标签名称"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_tags_on_app_id"
    t.index ["org_id"], name: "index_res_tags_on_org_id"
  end

  create_table "res_tags_relations", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "user_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["member_id"], name: "index_res_tags_relations_on_member_id"
    t.index ["org_id"], name: "index_res_tags_relations_on_org_id"
    t.index ["tag_id"], name: "index_res_tags_relations_on_tag_id"
    t.index ["user_id"], name: "index_res_tags_relations_on_user_id"
  end

  create_table "res_user_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_user_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_user_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_user_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_user_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_user_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_user_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_user_actions_on_user"
  end

  create_table "role_permission_relations", force: :cascade do |t|
    t.bigint "role_id"
    t.string "permission_type"
    t.bigint "permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_type", "permission_id"], name: "index_role_permission_relations_on_permission"
    t.index ["role_id"], name: "index_role_permission_relations_on_role_id"
  end

  create_table "role_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "role_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_role_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_role_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "role_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_role_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "role_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_role_permit_actions_on_user"
  end

  create_table "roles", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "mod_id"
    t.string "name", comment: "权限标识"
    t.string "label", comment: "显示名称"
    t.string "pinyin", comment: "拼音,排序用"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mod_id"], name: "index_roles_on_mod_id"
    t.index ["resource_type", "resource_id"], name: "index_roles_on_resource"
  end

  create_table "route_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "module名称"
    t.jsonb "conf", comment: "module导出路由"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_route_settings_on_app_id"
    t.index ["mod_id"], name: "index_route_settings_on_mod_id"
  end

  create_table "state_activate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "state_activate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_activate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_activate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_activate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_activate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_activate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_activate_actions_on_user"
  end

  create_table "state_bpm_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "state_bpm_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_bpm_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_bpm_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_bpm_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_bpm_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_bpm_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_bpm_actions_on_user"
  end

  create_table "state_events", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "transition_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type", comment: "STI"
    t.string "state"
    t.string "state_attr_name", comment: "状态机对应的模型属性名称"
    t.index ["app_id"], name: "index_state_events_on_app_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_events_on_eventable"
    t.index ["machine_id"], name: "index_state_events_on_machine_id"
    t.index ["source_id"], name: "index_state_events_on_source_id"
    t.index ["target_id"], name: "index_state_events_on_target_id"
    t.index ["transition_id"], name: "index_state_events_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_events_on_user"
  end

  create_table "state_machines", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "状态机名称"
    t.string "state_attr_name", comment: "状态机对应模型属性名称"
    t.string "klass", comment: "类名"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.string "flag", comment: "程序使用标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_machines_on_app_id"
  end

  create_table "state_places", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "state", comment: "节点状态"
    t.string "type", comment: "STI"
    t.integer "position", comment: "排序"
    t.jsonb "options", comment: "配置信息"
    t.jsonb "trigger_options", comment: "place的触发器处理"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_places_on_machine_id"
  end

  create_table "state_token_defines", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "token_define的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "type", comment: "STI"
    t.string "token_type", comment: "对应token的type"
    t.string "token_flag", comment: "对应token的flag"
    t.string "token_default_state", comment: "token生成的默认state"
    t.jsonb "token_form", comment: "token表单"
    t.jsonb "options", comment: "配置信息"
    t.jsonb "limit_options", comment: "限制要求配置信息，包括schedule循环、次数要求等"
    t.jsonb "user_options", comment: "限制可以操作的用户类型设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_token_defines_on_machine_id"
  end

  create_table "state_tokens", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "event_id"
    t.bigint "transition_id"
    t.bigint "token_define_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "处理节点名称"
    t.string "flag", comment: "处理节点flag"
    t.string "user_name", comment: "user的名称"
    t.string "state"
    t.jsonb "token_source_attributes", comment: "token source的attributes缓存"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_tokens_on_app_id"
    t.index ["event_id"], name: "index_state_tokens_on_event_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_tokens_on_eventable"
    t.index ["machine_id"], name: "index_state_tokens_on_machine_id"
    t.index ["token_define_id"], name: "index_state_tokens_on_token_define_id"
    t.index ["token_source_type", "token_source_id"], name: "index_state_tokens_on_token_source"
    t.index ["transition_id"], name: "index_state_tokens_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_tokens_on_user"
  end

  create_table "state_transitions", force: :cascade do |t|
    t.bigint "machine_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.bigint "terminate_place_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "type", comment: "STI"
    t.string "seq", comment: "transition的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "event_name", comment: "操作的英文名称"
    t.string "flag", comment: "程序使用的标记位"
    t.boolean "auto_trigger", comment: "是否自动触发"
    t.jsonb "options", comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
    t.jsonb "trigger_options", comment: "transition的触发器处理"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_transitions_on_machine_id"
    t.index ["source_id"], name: "index_state_transitions_on_source_id"
    t.index ["target_id"], name: "index_state_transitions_on_target_id"
    t.index ["terminate_place_id"], name: "index_state_transitions_on_terminate_place_id"
  end

  create_table "tanent_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "tanent_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_tanent_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_tanent_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "tanent_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_tanent_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "tanent_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_tanent_permit_actions_on_user"
  end

  create_table "tanent_resources", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.jsonb "payload", comment: "存额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanent_resources_on_app_id"
    t.index ["resource_type", "resource_id"], name: "index_tanent_resources_on_resource"
    t.index ["tanent_id"], name: "index_tanent_resources_on_tanent_id"
  end

  create_table "tanents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "code", comment: "租户标识"
    t.string "name", comment: "租户名称"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanents_on_app_id"
  end

  create_table "tofu_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "ancestry", comment: "树形结构"
    t.string "platform"
    t.string "layout", comment: "点击以后前端使用的layout"
    t.string "type", comment: "STI"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.text "icon", comment: "显示的图片或者图标"
    t.text "url", comment: "跳转地址，如果只是menu，可以为空"
    t.string "open_mode", comment: "打开页面的方式"
    t.integer "position", comment: "位置"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_type", "source_id"], name: "index_tofu_entries_on_source"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "ref_user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "account", comment: "账号，关联登录"
    t.string "name", comment: "用户姓名"
    t.string "nickname", comment: "用户昵称"
    t.string "pinyin", comment: "用户名拼音"
    t.string "mobile", comment: "用户手机号"
    t.string "email", comment: "用户邮箱"
    t.string "gender", comment: "性别"
    t.jsonb "avatar", comment: "用户头像"
    t.string "identity_id", comment: "证件号码，需要时候可以作为唯一标识"
    t.datetime "last_visit_at", comment: "最后访问时间"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "supervisor_id"
    t.date "birthday", comment: "生日"
    t.string "political", comment: "政治面貌"
    t.string "education", comment: "学位"
    t.string "degree", comment: "学历"
    t.index ["app_id"], name: "index_users_on_app_id"
    t.index ["ref_user_id"], name: "index_users_on_ref_user_id"
    t.index ["supervisor_id"], name: "index_users_on_supervisor_id"
    t.index ["tanent_id"], name: "index_users_on_tanent_id"
  end

  create_table "users_roles", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_users_roles_on_role_id"
    t.index ["user_id"], name: "index_users_roles_on_user_id"
  end

  create_table "version_relationships", force: :cascade do |t|
    t.bigint "app_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "real_resource_type"
    t.bigint "real_resource_id"
    t.string "version_type"
    t.bigint "version_id"
    t.string "operator_type"
    t.bigint "operator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_version_relationships_on_app_id"
    t.index ["operator_type", "operator_id"], name: "index_version_relationships_on_operator"
    t.index ["real_resource_type", "real_resource_id"], name: "index_version_relationships_on_real_resource"
    t.index ["resource_type", "resource_id"], name: "index_version_relationships_on_resource"
    t.index ["version_type", "version_id"], name: "index_version_relationships_on_version"
  end

end

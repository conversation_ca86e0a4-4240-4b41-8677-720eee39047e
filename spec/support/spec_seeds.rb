RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split(' ') || []
    @app = create :app
    @user = create(:user, app: @app)
    user_account_info = {
      account_type: 'User',
      account: @user.account,
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    @org = Org.create(name: 'org', app: @app)
    @department = Department.create(name: 'department', org: @org)

    create_list :inno_patent, 2, app: @app, creator: @user
    @inno_patents = Inno::Patent.all
    @inno_patent = @inno_patents.first
    @inno_patent_count = @inno_patents.count

    create_list :inno_transform, 2, app: @app, source: @inno_patent, creator: @user
    @inno_transforms = Inno::Transform.all
    @inno_transform = @inno_transforms.first
    @inno_transform_count = @inno_transforms.count

    create_list :inno_transform_item, 2, source: @inno_patent, creator: @user, transform: @inno_transform
    @inno_transform_items = Inno::TransformItem.all
    @inno_transform_item = @inno_transform_items.first
    @inno_transform_item_count = @inno_transform_items.count

    create_list :inno_paper, 2, app: @app, source: @inno_patent, creator: @user
    @inno_papers = Inno::Paper.all
    @inno_paper = @inno_papers.first
    @inno_paper_count = @inno_papers.count

    create_list :inno_ownership, 1, app: @app, resource: @inno_patent, user: @user
    @inno_ownerships = Inno::Ownership.all
    @inno_ownership = @inno_ownerships.first
    @inno_ownership_count = @inno_ownerships.count

    create_list :inno_work, 3, app: @app, creator: @user
    @inno_works = Inno::Work.all
    @inno_work = @inno_works.first
    @inno_work_count = @inno_works.count

    create_list :inno_software_copyright, 3, app: @app, creator: @user
    @inno_software_copyrights = Inno::SoftwareCopyright.all
    @inno_software_copyright = @inno_software_copyrights.first
    @inno_software_copyright_count = @inno_software_copyrights.count

    create_list :inno_award, 3, app: @app, creator: @user, source: @inno_patent
    @inno_awards = Inno::Award.all
    @inno_award = @inno_awards.first
    @inno_award_count = @inno_awards.count

    create_list :inno_competitive_product, 3, app: @app, creator: @user, source: @inno_patent
    @inno_competitive_products = Inno::CompetitiveProduct.all
    @inno_competitive_product = @inno_competitive_products.first
    @inno_competitive_product_count = @inno_competitive_products.count

    create_list :inno_scientific_research, 3, app: @app, creator: @user, source: @inno_patent
    @inno_scientific_researches = Inno::ScientificResearch.all
    @inno_scientific_research = @inno_scientific_researches.count
    @inno_scientific_research_count = @inno_scientific_researches.count

    create_list :inno_team_member, 1, app: @app, creator: @user, user: @user, department: @department, source: @inno_patent
    @inno_team_members = Inno::TeamMember.all
    @inno_team_member = @inno_team_members.count
    @inno_team_member_count = @inno_team_members.count

    create_list :inno_other_ip, 3, app: @app, creator: @user
    @inno_other_ips = Inno::OtherIp.all
    @inno_other_ip = @inno_other_ips.first
    @inno_other_ip_count = @inno_other_ips.count

    # create_list :inno_project, 2, app: @app, creator: @user
    # @inno_projects = Inno::Project.all
    # @inno_project = @inno_projects.first
    # @inno_project_count = @inno_projects.count

    @inno_project = Inno::Project.create(app: @app, creator: @user, name: 'test')

    create_list :inno_fund, 2, app: @app, creator: @user, project: @inno_project
    @inno_funds = Inno::Fund.all
    @inno_fund = @inno_funds.first
    @inno_fund_count = @inno_funds.count

    create_list :inno_setting, 2, app: @app
    @inno_settings = Inno::Setting.all
    @inno_setting = @inno_settings.first
    @inno_setting_count = @inno_settings.count

    create_list :inno_payment, 2, app: @app, patent: @inno_patent
    @inno_payments = Inno::Payment.all
    @inno_payment = @inno_payments.first
    @inno_payment_count = @inno_payments.count
  end
end

# RSpec
# spec/support/factory_bot.rb
RSpec.configure do |config|
  factories_directory = Rails.root.join('..', 'factories')
  engine_factory_directories = [RailsCom::Engine].map do |engine|
    engine.root.join('spec', 'factories')
  end
  FactoryBot.definition_file_paths = [factories_directory, *engine_factory_directories]
  FactoryBot.find_definitions
  config.include FactoryBot::Syntax::Methods
end

# Ensure the after_initialize callback is run during factory creation
FactoryBot::Strategy::Create.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance.save!
    instance
  end
end

FactoryBot::Strategy::Build.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance
  end
end

# FactoryBot::Strategy::AttributesFor.class_eval do
#   def result(evaluation)
#     evaluation.object.tap do |instance|
#       instance.run_callbacks(:initialize)
#     end.attributes.with_indifferent_access
#   end
# end

FactoryBot::Strategy::AttributesFor.class_eval do
  def result(evaluation)
    instance = evaluation.object.tap do |obj|
      obj.run_callbacks(:initialize)
    end

    # Convert keys to symbols and remove nil values
    instance.attributes.reject { |_, v| v.nil? }.with_indifferent_access
  end
end


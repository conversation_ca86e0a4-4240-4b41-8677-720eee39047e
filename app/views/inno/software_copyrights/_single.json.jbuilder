json.extract!(
  software_copyright,
  *software_copyright.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :name,
  :serial_no,
  :reg_no,
  :owner,
  :right_way,
  :right_range,
  :cert_no,
  :short_name,
  :version_no,
  :grant_date,
  :apply_identity,
  :apply_date,
  :apply_way,
  :soft_kind,
  :member_names,
  :complete_date,
  :pub_state,
  :contact_name,
  :contact_mobile,
  :convert_intention,
  :convert_way,
  :transform_state,
  :payload,
)

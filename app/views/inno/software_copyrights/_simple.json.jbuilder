json.partial! 'inno/software_copyrights/single', software_copyright: software_copyright
json.extract!(
  software_copyright,
  *software_copyright.class.try(:extra_view_attributes, 'simple'),
  :relate_department_ids,
  :relate_department_names,
)

json.has_star software_copyright.starred_by?(@current_user)
json.department_names software_copyright.department_names
json.manager_names software_copyright.manager_names
json.project_levels software_copyright.project_levels

json.partial! 'inno/funds/single', fund: fund
json.extract!(
  fund,
  *fund.class.try(:extra_view_attributes, 'simple'),
)

# json.app fund.app, partial: 'apps/single', as: :app
json.project fund.project, partial: 'inno/projects/single', as: :project
# json.creator fund.creator, partial: 'users/single', as: :creator

json.ta_statistics fund.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

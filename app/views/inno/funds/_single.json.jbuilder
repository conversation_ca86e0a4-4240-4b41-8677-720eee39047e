json.extract!(
  fund,
  *fund.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :project_id,
  :creator_id,
  :seq,
  :planned_at,
  :is_planned,
  :name,
  :state,
  :amount,
  :position,
  :payload,
  :attachment,
  :received_amount,
  :received_date,
  :cost_amount,
  :income_amount,
  :profit_amount,
  :tech_operator_amount,
  :tech_talent_amount,
  :group_assign_amount,
  :paid_unit,
  :paid_account,
  :paid_bank,
  :paid_date,
  :paid_num,
  :paid_ticket_date,
  :group_user_count,
)

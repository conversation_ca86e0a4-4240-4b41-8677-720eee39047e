json.partial! 'inno/projects/single', project: project
json.extract!(
  project,
  *project.class.try(:extra_view_attributes, 'simple'),
  :project_relations_statistics,
  :transform_type_project_tag_ids,
  :transform_sub_type_project_tag_ids,
  :payload_summary,
)

# json.app project.app, partial: 'apps/single', as: :app
json.creator project.creator, partial: 'users/single', as: :user
json.transform_type_project_tags project.transform_type_project_tags, partial: 'inno/project_tags/single', as: :project_tag
json.transform_sub_type_project_tags project.transform_sub_type_project_tags, partial: 'inno/project_tags/single', as: :project_tag

json.ta_statistics project.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.partial! 'inno/payments/single', payment: payment
json.extract!(
  payment,
  *payment.class.try(:extra_view_attributes, 'simple'),
)

# json.app payment.app, partial: 'apps/single', as: :app
json.patent payment.patent, partial: 'inno/patents/single', as: :patent
# json.user payment.user, partial: 'users/single', as: :user

json.ta_statistics payment.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.partial! 'inno/team_members/single', team_member: team_member
json.extract!(
  team_member,
  *team_member.class.try(:extra_view_attributes, 'simple'),
)

json.user team_member.user, partial: 'users/single', as: :user
# json.department team_member.department, partial: 'departments/single', as: :department

json.ta_statistics team_member.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

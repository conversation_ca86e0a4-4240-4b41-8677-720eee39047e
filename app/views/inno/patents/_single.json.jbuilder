json.extract!(
  patent,
  *patent.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :name,
  :apply_no,
  :apply_date,
  :pub_date,
  :pub_no,
  :abstract,
  :inventors,
  :ipc,
  :patent_type,
  :legal_state,
  :transform_state,
  :transforms_count,
  :payload,
  :expire_date,
  :patentee,
  :contact_name,
  :contact_mobile,
  :agency,
  :agency_code,
  :patentee_state,
  :state,
  :convert_intention,
  :convert_way,
  :payment,
  :payment_start_date,
  :payment_end_date,
)

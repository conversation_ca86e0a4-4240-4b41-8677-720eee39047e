json.extract!(
  work,
  *work.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :name,
  :serial_no,
  :category,
  :maker,
  :owner,
  :completion_date,
  :pub_date,
  :agency,
  :reg_date,
  :reg_no,
  :apply_date,
  :apply_identity,
  :obtain_rights_way,
  :rights_belong_way,
  :contact_name,
  :contact_mobile,
  :convert_intention,
  :convert_way,
  :pub_state,
  :transform_state,
  :payload,
)

class Inno::User::OtherIpsPolicy < ApplicationPolicy

  def distribute?
    record.creator_id == user.id && record.ownerships.where(role: 'edit').blank?
  end

  def update?
    record.permit_of_user(user) != 'view'
  end

  def disk_items_create?
    if context[:other_ip_id]
      user.app.inno_other_ips.find(context[:other_ip_id]).permit_of_user(user) != 'view'
    else
      record.permit_of_user(user) != 'view'
    end
  end

  def disk_items_update?
    if context[:other_ip_id]
      user.app.inno_other_ips.find(context[:other_ip_id]).permit_of_user(user) != 'view'
    else
      record.permit_of_user(user) != 'view'
    end
  end

  def disk_items_destroy?
    if context[:other_ip_id]
      user.app.inno_other_ips.find(context[:other_ip_id]).permit_of_user(user) != 'view'
    else
      record.permit_of_user(user) != 'view'
    end
  end

  class Scope < Scope
    def resolve
      scope.all
    end
  end
end

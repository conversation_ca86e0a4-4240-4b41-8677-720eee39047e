class Inno::User::TransformsPolicy < ApplicationPolicy

  def create?
    if context[:patent_id]
      user.app.inno_patents.find(context[:patent_id]).permit_of_user(user) != 'view'
    else
      record.source.permit_of_user(user) != 'view'
    end
  end

  def state_tokens_create?
    record.source.permit_of_user(user) != 'view'
  end

  def state_tokens_update?
    record.source.permit_of_user(user) != 'view'
  end

  def state_events_create?
    record.source.permit_of_user(user) != 'view'
  end

  class Scope < Scope
    def resolve
      scope.all
    end
  end
end
module Inno::Transformable
  extend ActiveSupport::Concern

  included do
    has_many :transforms, as: :source

    attribute :transforms_count, :integer, comment: '转化次数'
    attribute :transform_state, :string, comment: '转化状态'

    has_many :project_relations, as: :source, class_name: 'Inno::ProjectRelation'
    has_many :projects, through: :project_relations

    default_value_for(:transform_state) { 'pending' }

    enum transform_state: {
      pending: 'pending',
      processing: 'processing',
      transformed: 'transformed',
      suspended: 'suspended',
      abandoned: 'abandoned',
    }

    after_touch :update_transform_state

    def update_transform_state
      grouped_data = projects.group(:state).count
      if (grouped_data["transformed"] || 0) > 0
        update_column(:transform_state, 'transformed')
      elsif (grouped_data["processing"] || 0) > 0
        update_column(:transform_state, 'processing')
      else
        update_column(:transform_state, 'pending')
      end
    end

    def last_transform
      transforms.order(id: :desc).first
    end
  end

  class_methods do
  end
end

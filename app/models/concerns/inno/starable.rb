module Inno::Starable
  extend ActiveSupport::Concern

  included do
    include Favor::Ext::Markable

    attribute :stars_count, :integer, comment: '收藏次数'

    def starred_by?(user)
      star_mark_actions.exists?(target: user)
    end
  end

  class_methods do
    def acts_as_starable(mark_name:, mark_class_name:)
      acts_as_markable(mark_name: mark_name, mark_class_name: mark_class_name, require_folder: false)

      scope :star_by, ->(user) {
        source_ids = user.mark_actions.where(user_type: mark_class_name).pluck(:user_id)
        where(id: source_ids)
      }
    end
  end
end
module Inno::Managable
  extend ActiveSupport::Concern

  included do
    belongs_to :creator, class_name: '::User'

    has_many :ownerships, as: :resource, dependent: :destroy
    has_many :users, through: :ownerships
    has_many :departments, through: :ownerships

    after_create :create_main_ownership

    scope :manage_by, ->(user) {
      if !user.has_inno_role?
        joins(:ownerships).where(ownerships: { user: user })
      elsif !user.is_inno_admin?
        joins(:ownerships).where(ownerships: { user: user.inno_relate_permit_resources })
      end
    }

    scope :department_ids_in, ->(*ids) {
      if ids.include?('all')
        joins(:departments)
      else
        joins(departments: :ancestor_hierarchies).where(ancestor_hierarchies:  { ancestor_id: ids })
      end
    }

    def permit_of_user(user)
      ownerships.find_by(user: user)&.role || 'view'
    end

    def disk_item_permit(user)
      %w(main edit).include?(permit_of_user(user)) ? 'manage' : 'view'
    end

    def create_main_ownership
      ownerships.where(role: 'main').first_or_create!(user: creator, department: creator.departments.first)
    end

    def department_names
      departments.pluck(:name).uniq
    end

    def manager_names
      users.pluck(:name).uniq
    end
  end

  class_methods do
    def ransackable_scopes(auth_object = nil)
      super(auth_object).concat(
        %i[department_ids_in]
      )
    end

    def ransackable_scopes_skip_sanitize_args
      %i[department_ids_in]
    end
  end
end
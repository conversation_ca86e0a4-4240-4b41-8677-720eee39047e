module Inno::Model::Payment
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :source, polymorphic: true, optional: true
    belongs_to :patent, optional: true
    belongs_to :user, class_name: '::User', optional: true

    stiable
    seqable
    effectable effective_at: 'start_date', invalid_at: 'end_date', field_type: 'date'

    attribute :name,       :string, comment: '名称'
    attribute :state,      :string, comment: '状态'
    attribute :amount,     :decimal, scale: 5, precision: 20, comment: '金额'
    attribute :paid_at,    :datetime, comment: '支付时间'
    attribute :paid_type,  :string,   comment: '支付方式'
    attribute :attachment, :jsonb,    comment: '附件'
    attribute :payload,    :jsonb,    comment: '其他字段'

    enum state: {
      paid: 'paid',
      canceled: 'canceled',
      expired: 'expired',
    }

    default_value_for(:state){ 'paid' }
    default_value_for(:app) { |o| o.patent&.app || o.user&.app }

    after_save    :reset_patent_payment_date!, if: :patent
    after_destroy :reset_patent_payment_date!, if: :patent

    # 重置专利的使用期限
    def reset_patent_payment_date!
      payment = patent.reload.payments.order(end_date: :desc).first
      payment ? patent.update(payment_end_date: end_date, payment: true) : patent.update(payment_end_date: nil, payment: false)
    end
  end

  class_methods do
  end
end
module Inno::Model::Setting
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    class Option
      include AttrJson::Model
      attr_json_config(bad_cast: :as_nil, unknown_key: :allow)

      attr_json :income_rate, :float
      attr_json :tech_operator_rate, :float
      attr_json :tech_talent_rate, :float
    end

    belongs_to :app

    attribute :name,     :string,  comment: '名称'
    attribute :state,    :string,  comment: '状态'
    attribute :position, :integer, comment: '排序'
    attribute :option,   :jsonb,   comment: '配置'
    serialize :option, coder: Option.to_serialization_coder, default: {}

    enum state: {
      used: 'used',
      closed: 'closed'
    }

    default_value_for(:state){ 'used' }
  end
end
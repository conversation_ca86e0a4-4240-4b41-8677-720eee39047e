module Inno::Model::Fund
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    seqable

    belongs_to :app
    belongs_to :project, optional: true, touch: true
    belongs_to :creator, class_name: '::User'

    has_event :plan

    # 节本信息
    attribute :name,  :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :amount,     :decimal, scale: 5, precision: 20, comment: '金额'
    attribute :position,   :integer, comment: '排序'
    attribute :payload,    :jsonb,   comment: '其他字段'
    attribute :attachment, :jsonb,   comment: '附件'
    # 收款信息
    attribute :received_amount,  :decimal, scale: 5, precision: 20, comment: '到账金额'
    attribute :received_date,    :date,   comment: '收款日期'
    # 成本收益
    attribute :cost_amount,   :decimal, scale: 5, precision: 20, comment: '成本'
    attribute :income_amount, :decimal, scale: 5, precision: 20, comment: '收入'
    attribute :profit_amount, :decimal, scale: 5, precision: 20, comment: '收益'
    # 运营金额
    attribute :tech_operator_amount, :decimal, scale: 5, precision: 20, comment: '运营金额'
    attribute :tech_talent_amount, :decimal, scale: 5, precision: 20, comment: '人才培养金额'
    # 分配金额
    attribute :group_assign_amount, :decimal, scale: 5, precision: 20, comment: '分配金额'
    attribute :group_user_count, :integer, comment: '项目组分配人数'
    # 支付信息
    attribute :paid_unit,         :string, comment: '支付单位'
    attribute :paid_account,      :string, comment: '支付账户'
    attribute :paid_bank,         :string, comment: '支付银行'
    attribute :paid_date,         :date,   comment: '支付日期'
    attribute :paid_num,          :string, comment: '支付流水号' # 支付流水号
    attribute :paid_ticket_date,  :string, comment: '开票时间' # 支付流水号

    attribute :meta, :jsonb, comment: '配置字段'

    # 更新金额
    after_update :calc_amount, if: -> { saved_change_to_received_amount? || saved_change_to_cost_amount? }
    after_create :calc_amount, if: -> { received_amount? && cost_amount? }

    before_save :set_state

    enum state: {
      submitted: 'submitted', # 已计划
      pending: 'pending', # 待支付
      paid: 'paid', # 已支付
    }

    acts_as_list scope: [:project_id]

    default_value_for(:state){ 'submitted' }
    default_value_for(:app){ |o| o.project&.app || o.creator&.app }

    def set_state
      self.state = 'pending' if amount && submitted?
    end

    def state_zh
      case state
        when 'submitted'
          '已计划'
        when 'pending'
          '待支付'
        when 'paid'
          '已支付'
        else
          '未知'
      end
    end

    def calc_setting
      app.inno_settings.used.last
    end

    # 计算金额
    def calc_amount
      setting = calc_setting
      if setting
        profit_amount = received_amount.to_f - cost_amount.to_f
        income_amount = profit_amount * setting.option.income_rate
        tech_operator_amount = income_amount * setting.option.tech_operator_rate
        tech_talent_amount = income_amount * setting.option.tech_talent_rate

        update_columns(
          profit_amount: profit_amount,
          income_amount: income_amount,
          tech_operator_amount: tech_operator_amount,
          tech_talent_amount: tech_talent_amount,
          meta: setting.option.as_json
        )
      end
    end
  end

  class_methods do
    def amount_info association=nil
      association ||= all
      total = association.sum(:amount)
      received = association.paid.sum(:received_amount)
      {
        total: total,
        received: received,
        total_w: (total * 1.0 / 10000).round(2),
        received_w: (received * 1.0 / 10000).round(2),
      }
    end
  end
end

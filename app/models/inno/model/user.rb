module Inno::Model::User
  extend ActiveSupport::Concern

  included do
    
    has_many :inno_ownerships, class_name: 'Inno::Ownership', as: :user
    has_many :inno_patents, through: :inno_ownerships, source: :resource, source_type: 'Inno::Patent'

    scope :under_department_id_eq, ->(id) {
      joins(departments: :ancestor_hierarchies).where(ancestor_hierarchies: { ancestor_id: id})
    }

    scope :manage_by, ->(user) {
      if !user.is_inno_admin?
        joins(:memberships).where(memberships: { department_id: user.inno_manage_departments.ids })
      end
    }

    def inno_manage_departments
      app.departments.manage_by(self)
    end

    def inno_direct_manage_department_ids
      duty_ids = Role.find_by(name: 'inno_manage').duty_ids
      memberships.where.not(department_id: nil).where(duty_id: duty_ids).pluck(:department_id)
    end

    def extra_patent_permitted_attributes
      has_inno_role? ? [user_ids: []] : []
    end

    def all_relation_role_names
      all_relation_roles.map(&:name)
    end

    def has_inno_role?
      (all_relation_role_names & %w(inno_admin inno_manage)).count > 0
    end

    def is_inno_admin?
      all_relation_role_names.include?('inno_admin')
    end

    def is_inno_manager?
      all_relation_role_names.include?('inno_manage')
    end

    def inno_relate_permit_resources
      permit_array = []
      permit_array.append(self)
      permit_array.append(*inno_manage_departments)
      permit_array
    end
  end

  class_methods do
    def ransackable_scopes(auth_object = nil)
      super(auth_object).concat(
        %i[under_department_id_eq]
      )
    end

    def ransackable_scopes_skip_sanitize_args
      %i[under_department_id_eq]
    end

  end
end
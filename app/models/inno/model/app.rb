module Inno::Model::App
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    has_many :inno_patents, class_name: 'Inno::Patent', dependent: :destroy
    has_many :inno_transforms, class_name: 'Inno::Transform', dependent: :destroy
    has_many :inno_papers, class_name: 'Inno::Paper', dependent: :destroy
    has_many :inno_ownerships, class_name: 'Inno::Ownership', dependent: :destroy
    has_many :inno_works, class_name: 'Inno::Work', dependent: :destroy
    has_many :inno_software_copyrights, class_name: 'Inno::SoftwareCopyright', dependent: :destroy
    has_many :inno_other_ips, class_name: 'Inno::OtherIp', dependent: :destroy
    has_many :inno_settings, class_name: 'Inno::Setting', dependent: :destroy
  end

  class_methods do
  end
end

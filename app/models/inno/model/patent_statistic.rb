module Inno::Model::PatentStatistic
  extend ActiveSupport::Concern

  included do
  end

  class_methods do
    def workbench_stat(params = {})
      user = params[:user]
      managed_patents = user.app.inno_patents.manage_by(user).distinct
      {
        managed_count: managed_patents.count,
        stared_count: user.app.inno_patents.star_by(user).count,
        patent_type: managed_patents.group(:patent_type).count,
        legal_state: managed_patents.group(:legal_state).count,
        transform_state: managed_patents.group(:transform_state).count,
        transform_amount: user.app.inno_transforms.where(source: managed_patents).amount_stat,
        expired_in_three_months: managed_patents
          .where("expire_date >= ? and expire_date <= ?", Date.today - 3.months, Date.today)
          .group(:patent_type)
          .count,
      }
    end

    def dashboard_stat(params = {})
      prev_month_num = where('inno_patents.created_at < ?', Date.today.beginning_of_month).count
      current_num = count
      {
        patent_num: {
          current: current_num,
          month_add: count - prev_month_num,
          growth_percent: "%.2f" % ((current_num / (prev_month_num == 0 ? 1 : prev_month_num) - 1) * 100),
        },
        legal_state: group(:legal_state).count,
        transform_state: group(:transform_state).count,
      }
    end

    def group_stat(params = {})
      {
        patent_type: group(:patent_type).count,
        legal_state: group(:legal_state).count,
        department: joins(:departments).group("departments.name").count
      }
    end

    def type_ip_state_stat(params = {})
      stat = {}
      select("inno_patents.patent_type, inno_patents.ip_state, count(*) as num")
        .group("inno_patents.patent_type, inno_patents.ip_state")
        .each do |record|
          stat[record.patent_type] ||= {}
          stat[record.patent_type][record.ip_state] = record.num
        end
      stat
    end

    def type_transform_state_stat(params = {})
      stat = {}
      select("inno_patents.patent_type, inno_patents.transform_state, count(*) as num")
        .group("inno_patents.patent_type, inno_patents.transform_state")
        .each do |record|
          stat[record.patent_type] ||= {}
          stat[record.patent_type][record.transform_state] = record.num
        end
      stat
    end

    # 待删除
    def type_state_stat(params = {})
      legal_state, use_state = {}, {}
      select("inno_patents.patent_type, inno_patents.legal_state, count(*) as num")
        .group("inno_patents.patent_type, inno_patents.legal_state")
        .each do |record|
          legal_state[record.patent_type] ||= {}
          legal_state[record.patent_type][record.legal_state] = record.num
        end
      select("inno_patents.patent_type, inno_patents.payload ->> 'state' as state, count(*) as num")
        .where("inno_patents.payload ->> 'state' is not null")
        .group("inno_patents.patent_type, inno_patents.payload ->> 'state'")
        .each do |record|
          use_state[record.patent_type] ||= {}
          use_state[record.patent_type][record.state] = record.num
        end
      { legal_state: legal_state, use_state: use_state }
    end

    def year_pub_trend(params = {})
      patents = select("
        to_char(inno_patents.pub_date, 'YYYY') as year,
        count(CASE WHEN inno_patents.patent_type = 'design' THEN 1 END) as design_num,
        count(CASE WHEN inno_patents.patent_type = 'utility_model' THEN 1 END) as utility_model_num,
        count(CASE WHEN inno_patents.patent_type = 'invention' THEN 1 END) as invention_num,
        count(*) as patent_num
      ").group('year')
        .map do |record|
          [
            record.year,
            {
              design_num: record.design_num.to_i,
              utility_model_num: record.utility_model_num.to_i,
              invention_num: record.invention_num.to_i,
              patent_num: record.patent_num.to_i
            }
          ]
        end.to_h

      softwares = Inno::SoftwareCopyright.year_pub_trend
      works = Inno::Work.year_pub_trend

      (patents.keys + softwares.keys + works.keys).uniq.map(&:to_i).sort.map do |year|
        stat = patents[year.to_s] || {}
        stat.merge(
          year: year,
          software: softwares[year.to_s].to_i,
          work: works[year.to_s].to_i,
          patent_num: stat[:patent_num].to_i + softwares[year.to_s].to_i + works[year.to_s].to_i
        )
      end
    end
  end
end
module Inno::Model::Project
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable # 项目所属科室，甲方，乙方，丙方
    seqable

    belongs_to :app
    belongs_to :creator, class_name: '::User'

    has_many :funds, class_name: 'Inno::Fund', dependent: :destroy
    has_many :project_relations, class_name: 'Inno::ProjectRelation', dependent: :destroy
    accepts_nested_attributes_for :project_relations, allow_destroy: true

    # transform_type_project_tag_ids=
    action_store :transform_type, :project_tag, class_name: 'Inno::ProjectTag', action_class_name: 'Inno::TagAction'
    action_store :transform_sub_type, :project_tag, class_name: 'Inno::ProjectTag', action_class_name: 'Inno::TagAction'

    attribute :name, :string, comment: '项目名称'
    # attribute :project_type, :string, comment: '项目类型'
    attribute :leader_name, :string, comment: '负责人姓名'
    attribute :contract_amount, :decimal, comment: '合同金额'
    attribute :actual_amount, :decimal, comment: '实到金额'
    attribute :payload, :jsonb, comment: '信息'
    attribute :state, :string, default: 'processing', comment: '状态'
    attribute :start_at, :datetime, comment: '开始时间'

    mergeable :payload

    default_value_for(:app) { |t| t.creator.app }
    default_value_for(:state) { 'processing' }

    before_validation :set_default_values
    after_save :touch_project_relation_source, if: :saved_change_to_state?

    enum state: {
      processing: 'processing',
      transformed: 'transformed',
      abandoned: 'abandoned',
    }

    scope :manage_by, ->(user) {
      where(source: user.app.inno_patents.manage_by(user))
    }

    scope :finished, -> {
      where(state: %w[abandoned transformed])
    }

    scope :unfinished, -> {
      where(state: %w[processing])
    }

    after_touch :update_actual_amount

    def update_actual_amount
      self.actual_amount = funds.paid.sum(:received_amount)
      save if changed?
    end

    def project_relations_statistics
      # patent patent_type 专利类型
      # work category 作品类别
      # other_ip kind 知识产权类型
      {
        all: project_relations.group(:source_type).count,
        'Inno::Patent' => Inno::Patent.where(id: project_relations.select(:source_id)).group(:patent_type).count,
        'Inno::Work' => Inno::Work.where(id: project_relations.select(:source_id)).group(:category).count,
        'Inno::OtherIp' => Inno::OtherIp.where(id: project_relations.select(:source_id)).group(:kind).count,
      }
    end

    def payload_summary
      {
        '签约时间' => payload&.dig('合同签订', '签约时间'),
      }
    end

    def payload_statistics
      (payload || {}).each_with_object({}) do |(key, value), hash|
        hash[key] = value.present?
        hash
      end
    end

    def set_default_values
      self.model_payload ||= {}
      self.model_payload['关联知识产权概览'] ||= project_relations.map do |relation|
        relation.source ?
          "#{relation.source.type_zh}：#{relation.source.name}#{relation.source.try(:apply_no) ? "（#{relation.source.try(:apply_no)}）" : ""}" :
          nil
      end.compact.join("\n")
    end

    def touch_project_relation_source
      project_relations.each do |relation|
        relation.source.touch
      end
    end
  end

  class_methods do
  end
end

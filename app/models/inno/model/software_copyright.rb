module Inno::Model::SoftwareCopyright
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Inno::Model::Ip
    include Inno::Managable
    include Inno::Transformable
    include Inno::Departmentable

    include Inno::Starable
    acts_as_starable(mark_name: 'inno_software_copyright', mark_class_name: name)

    include Disk::Ext::Diskable
    has_many :disk_items, class_name: 'Disk::Item', as: :source

    has_many :scientific_researches, as: :source, dependent: :destroy

    belongs_to :app

    attribute :name, :string, comment: '软件名称'
    attribute :short_name, :string, comment: '软件简称'
    attribute :version_no, :string, comment: '版本号'
    attribute :grant_date, :date, comment: '授权日'
    attribute :apply_identity, :string, comment: '办理身份'
    attribute :owner, :string, comment: '著作权人'
    attribute :member_names, :string, comment: '团队成员'
    attribute :cert_no, :string, comment: '证书号'
    attribute :reg_no, :string, comment: '登记号'
    attribute :serial_no, :string, comment: '流水号'
    attribute :right_way, :string, comment: '权利取得方式'
    attribute :right_range, :string, comment: '权利范围'
    attribute :apply_date, :date, comment: '申请日'
    attribute :apply_way, :string, comment: '申请方式'
    attribute :soft_kind, :string, comment: '软件分类'
    attribute :complete_date, :date, comment: '开发完成日期'
    attribute :pub_state, :string, comment: '发表状态'
    attribute :contact_name, :string, comment: '联系人'
    attribute :contact_mobile, :string, comment: '联系方式'
    attribute :convert_intention, :string, comment: '转化意向'
    attribute :convert_way, :string, comment: '转化方式'
    attribute :payload, :jsonb, comment: ''

    has_paper_trail_version(
      ignore: [:transforms_count, :transform_state, :stars_count],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

    def project_levels
      scientific_researches.pluck(:level).uniq.compact
    end

    def type_zh
      '软著'
    end
  end

  class_methods do
    def year_pub_trend
      software = select("
        to_char(inno_software_copyrights.grant_date, 'YYYY') as year,
        count(*) as num
      ").group("year").map do |record|
          [record.year, record.num.to_i]
        end.to_h
    end
  end
end

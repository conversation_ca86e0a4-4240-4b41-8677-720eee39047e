module Inno::Model::Patent
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Inno::Model::Ip
    include Inno::Managable
    include Inno::Transformable
    include Inno::Departmentable

    include Inno::Starable
    acts_as_starable(mark_name: 'inno_patent', mark_class_name: name)

    formable
    effectable effective_at: 'payment_start_date', invalid_at: 'payment_end_date', field_type: 'date'

    belongs_to :app

    include Disk::Ext::Diskable
    has_many :disk_items, class_name: 'Disk::Item', as: :source

    has_many :scientific_researches, as: :source, dependent: :destroy
    has_many :payments, dependent: :destroy

    attribute :name, :string, comment: '专利名称'
    attribute :patent_type, :string, comment: '专利类型'
    attribute :pub_date, :date, comment: '公开/公告日期'
    attribute :apply_date, :date, comment: '申请日期'
    attribute :apply_no, :string, comment: '专利申请号'
    attribute :ipc, :string, comment: 'IPC分类号'
    attribute :patentee, :string, comment: '专利权人'
    attribute :inventors, :string, comment: '发明人'
    attribute :contact_name, :string, comment: '联系人'
    attribute :contact_mobile, :string, comment: '联系电话'
    attribute :agency, :string, comment: '代理机构'
    attribute :agency_code, :string, comment: '代理机构代码'
    attribute :patentee_state, :string, comment: '专利权人状态'
    attribute :legal_state, :string, comment: '法律状态'
    attribute :state, :string, comment: '专利权状态'
    attribute :expire_date, :date, comment: '失效日期'
    attribute :convert_intention, :string, comment: '转化意向'
    attribute :convert_way, :string, comment: '转化意向'
    attribute :abstract, :text, comment: '专利摘要'
    attribute :payload, :jsonb, comment: '额外信息'
    attribute :pub_no, :string, comment: '公开号'
    attribute :payment, :boolean, comment: '专利是否需要付费使用'

    default_value_for(:payment) { false }

    enum patent_type: {
      invention: 'invention',
      utility_model: 'utility_model',
      design: 'design'
    }

    enum legal_state: {
      valid: 'valid',
      invalid: 'invalid',
      review: 'review',
      pending: 'pending'
    }, _prefix: true

    has_paper_trail_version(
      ignore: [:transforms_count, :transform_state, :stars_count],
      meta: {
        model_info: :version_model_info
      }
    )

    scope :will_expired, -> {
      where(payment: true).ransack(payment_end_date_lt: 30.days.since.to_date).result
    }

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

    def users_brief
      users.map do |user|
        { id: user.id, name: user.name }
      end
    end

    def project_levels
      scientific_researches.pluck(:level).uniq.compact
    end

    def type_zh
      '专利'
    end

    def payment_state
      return 'normal' unless payment
      return 'normal' unless payment_end_date
      if payment_end_date < Date.today
        'expired'
      elsif payment_end_date < 30.days.since.to_date
        'will_expired'
      else
        'normal'
      end
    end
  end

  class_methods do
  end
end

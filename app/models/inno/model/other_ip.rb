module Inno::Model::OtherIp
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Inno::Managable
    include Inno::Transformable
    include Inno::Starable
    include Inno::Departmentable

    acts_as_starable(mark_name: 'inno_work', mark_class_name: name)

    include Disk::Ext::Diskable
    has_many :disk_items, class_name: 'Disk::Item', as: :source

    has_many :scientific_researches, as: :source, dependent: :destroy

    belongs_to :app

    attribute :name, :string, comment: '名称'
    attribute :kind, :string, comment: '类型'
    attribute :grant_date, :date, comment: '授权日'
    attribute :apply_date, :date, comment: '申请日'
    attribute :apply_no, :string, comment: '申请号'
    attribute :cert_no, :string, comment: '证书号'
    attribute :obligee, :string, comment: '权利人'
    attribute :inventor, :string, comment: '发明人/设计人'
    attribute :agency, :string, comment: '代理机构'
    attribute :contact_name, :string, comment: '联系人'
    attribute :contact_mobile, :string, comment: '联系方式'
    attribute :convert_intention, :string, comment: '转化意向'
    attribute :convert_way, :string, comment: '转化方式'
    attribute :payload, :jsonb, comment: ''

    has_paper_trail_version(
      ignore: [:transforms_count, :transform_state, :stars_count],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

    def project_levels
      scientific_researches.pluck(:level).uniq.compact
    end

    def type_zh
      '其他'
    end
  end

  class_methods do
  end
end

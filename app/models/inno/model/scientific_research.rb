module Inno::Model::ScientificResearch
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    acts_as_list(scope: :source, column: :position)

    belongs_to :app
    belongs_to :creator, class_name: '::User'
    belongs_to :source, polymorphic: true

    attribute :name, :string, comment: '课题名称'
    attribute :num, :string, comment: '课题编号'
    attribute :leader, :string, comment: '项目负责人'
    attribute :undertaking_unit, :string, comment: '承担单位'
    attribute :level, :string, comment: '项目级别'
    attribute :from, :string, comment: '项目来源'
    attribute :kind, :string, comment: '项目类型'
    attribute :init_at, :date, comment: '立项时间'
    attribute :start_at, :date, comment: '开始时间'
    attribute :end_at, :date, comment: '结束时间'
    attribute :funds, :decimal, comment: '项目经费'
    attribute :financial_support, :string, comment: '财政课题资助'
    attribute :payload, :jsonb, comment: ''
    attribute :position, :integer, comment: '排序'

    default_value_for(:app) { |sr| sr.creator&.app }

    has_paper_trail_version(
      version_belongs: [:source],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

  end

  class_methods do
  end
end

module Inno::Model::Award
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    acts_as_list(scope: :source, column: :position)

    belongs_to :app
    belongs_to :creator, class_name: '::User'
    belongs_to :source, polymorphic: true

    attribute :name, :string, comment: '获奖名称'
    attribute :year, :integer, comment: '年份'
    attribute :grant_unit, :string, comment: '授奖单位'
    attribute :level, :string, comment: '奖励级别'
    attribute :completer, :string, comment: '完成人'
    attribute :position, :integer, comment: '排序'

    default_value_for(:app) { |a| a.creator&.app }

    has_paper_trail_version(
      version_belongs: [:source],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end
  end

  class_methods do
  end
end

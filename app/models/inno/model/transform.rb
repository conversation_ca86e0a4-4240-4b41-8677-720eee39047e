module Inno::Model::Transform
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include State::Ext::Eventable
    acts_as_state

    belongs_to :app
    belongs_to :source, polymorphic: true, counter_cache: true
    belongs_to :creator, class_name: '::User'
    belongs_to :patent, -> { where(inno_transforms: { source_type: 'Inno::Patent' }) }, foreign_key: :source_id, optional: true

    has_many :transform_items, dependent: :destroy

    attribute :contract_amount, :decimal, comment: '合同金额'
    attribute :actual_amount, :decimal, comment: '实到金额'
    attribute :payload, :jsonb, comment: '额外信息'

    mergeable :payload

    default_value_for(:app) { |t| t.source&.app }
    default_value_for(:state) { 'processing' }

    scope :manage_by, ->(user) {
      where(source: user.app.inno_patents.manage_by(user))
    }

    scope :finished, -> {
      where(state: %w(abandoned transformed))
    }

    scope :unfinished, -> {
      where(state: %w(processing))
    }

    after_save_commit :update_patent_transform_state

    def update_patent_transform_state
      source.update(transform_state: state) if source.has_attribute?(:transform_state)
    end

    def on_success(**args)
      update(
        contract_amount: args[:contract_amount],
        actual_amount: args[:actual_amount],
        payload: {
          transformed: args
        }
      )
    end
  end

  class_methods do
  end
end
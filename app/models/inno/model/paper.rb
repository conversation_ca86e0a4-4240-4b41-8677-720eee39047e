module Inno::Model::Paper
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :creator, class_name: '::User'
    belongs_to :source, polymorphic: true

    has_paper_trail_version(
      version_belongs: [:source],
      meta: {
        model_info: :version_model_info
      }
    )

    acts_as_list(scope: :source, column: :position)

    attribute :name, :string, comment: '论文名称'
    attribute :author, :string, comment: '作者'
    attribute :journal_name, :string, comment: '期刊名称'
    attribute :jcr, :string, comment: 'JCR分区'
    attribute :year, :integer, comment: '发表年份'
    attribute :pmid, :string, comment: 'PMID'
    attribute :intro, :text, comment: '简介'
    attribute :position, :integer, comment: '排序位置'

    default_value_for(:app) { |a| a.creator&.app }

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

  end

  class_methods do
  end
end

module Inno::Model::TeamMember
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    acts_as_list(scope: :source)

    belongs_to :app
    belongs_to :source, polymorphic: true
    belongs_to :user, class_name: '::User'
    # 弃用 department
    belongs_to :department, class_name: '::Department', optional: true
    belongs_to :creator, class_name: '::User'

    attribute :role, :string, comment: '团队角色'
    attribute :position, :integer, comment: '排序'

    validates :user_id, uniqueness: { scope: [:source_id, :source_type], message: '团队成员已存在' }

    default_value_for(:app) { |t| t.user&.app }


    has_paper_trail_version(
      version_belongs: [:source],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        user_name: user&.name,
        # department_name: department&.name,
        type: class_name,
      }
    end
  end

  class_methods do
  end
end

module Inno::Model::Work
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Inno::Model::Ip
    include Inno::Managable
    include Inno::Transformable
    include Inno::Departmentable

    include Inno::Starable
    acts_as_starable(mark_name: 'inno_work', mark_class_name: name)

    include Disk::Ext::Diskable
    has_many :disk_items, class_name: 'Disk::Item', as: :source

    has_many :scientific_researches, as: :source, dependent: :destroy

    belongs_to :app

    attribute :name, :string, comment: '作品名称'
    attribute :category, :string, comment: '作品类别'
    attribute :reg_date, :date, comment: '登记日期'
    attribute :owner, :string, comment: '著作权人'
    attribute :maker, :string, comment: '制片者'
    attribute :reg_no, :string, comment: '登记号'
    attribute :serial_no, :string, comment: '流水号'
    attribute :apply_date, :date, comment: '申请日期'
    attribute :apply_identity, :string, comment: '办理身份'
    attribute :agency, :string, comment: '代理机构'
    attribute :obtain_rights_way, :string, comment: '权利取得方式'
    attribute :rights_belong_way, :string, comment: '权利归属方式'
    attribute :contact_name, :string, comment: '联系人'
    attribute :contact_mobile, :string, comment: '联系电话'
    attribute :convert_intention, :string, comment: '转化意向'
    attribute :convert_way, :string, comment: '转化意向'
    attribute :completion_date, :date, comment: '创作完成日期'
    attribute :pub_date, :date, comment: '首次公映日期'
    attribute :pub_state, :string, comment: '发表状态'
    attribute :payload, :jsonb, comment: '额外信息'

    has_paper_trail_version(
      ignore: [:transforms_count, :transform_state, :stars_count],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end

    def project_levels
      scientific_researches.pluck(:level).uniq.compact
    end

    def type_zh
      '作品'
    end
  end

  class_methods do
    def year_pub_trend
      software = select("
        to_char(inno_works.completion_date, 'YYYY') as year,
        count(*) as num
      ").group("year").map do |record|
          [record.year, record.num.to_i]
        end.to_h
    end
  end
end

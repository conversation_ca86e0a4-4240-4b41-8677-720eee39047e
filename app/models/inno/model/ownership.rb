module Inno::Model::Ownership
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :resource, polymorphic: true
    belongs_to :user, class_name: '::User'
    belongs_to :department, class_name: '::Department', optional: true

    attribute :role, :string, comment: "角色"

    default_value_for(:app) { |o| o.resource&.app }
  end

  class_methods do
  end
end
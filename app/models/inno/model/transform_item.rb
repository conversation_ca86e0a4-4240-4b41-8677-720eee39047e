module Inno::Model::TransformItem
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :transform
    belongs_to :source, polymorphic: true
    belongs_to :creator, class_name: '::User'

    attribute :actual_amount, :decimal, comment: '实到金额'
    attribute :payload, :jsonb, comment: '额外信息'

    default_value_for(:source) { |t| t.transform&.source }

    after_save_commit :update_transform

    def update_transform
      transform.update(actual_amount: actual_amount)
    end

  end

  class_methods do
  end
end
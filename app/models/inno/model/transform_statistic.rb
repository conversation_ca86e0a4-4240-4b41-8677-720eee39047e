module Inno::Model::TransformStatistic
  extend ActiveSupport::Concern

  included do
  end

  class_methods do
    def transaction_stat(params)
      transforms = where(state: 'transformed')
      prev_transforms = transforms.where("updated_at < ?", Date.today.beginning_of_month)

      transform_res = transforms.select("count(*) as transform_num, sum(contract_amount) as total_amount")[0]
      prev_transform_res = prev_transforms.select("count(*) as transform_num, sum(contract_amount) as total_amount")[0]

      actual_amount = transforms.sum(:actual_amount).to_f

      transform_num = transform_res.transform_num.to_i
      transform_amount = transform_res.total_amount.to_f

      prev_transform_num = prev_transform_res.transform_num.to_i
      prev_transform_amount = prev_transform_res.total_amount.to_f

      {
        transform_num: {
          current: transform_num,
          prev_month: prev_transform_num,
          month_add: transform_num - prev_transform_num,
          growth_percent: '%.2f' % ((transform_num.to_f / (prev_transform_num == 0 ? 1 : prev_transform_num) - 1) * 100)
        },
        transform_amount: {
          current: transform_amount,
          prev_month: prev_transform_amount,
          month_add: transform_amount - prev_transform_amount,
          growth_percent: '%.2f' % ((transform_amount / (prev_transform_amount == 0 ? 1 : prev_transform_amount) - 1) * 100)
        },
        actual_amount: {
          current: actual_amount
        },
        pending_amount: {
          current: (transform_amount - actual_amount) < 0 ? 0 : (transform_amount - actual_amount)
        }
      }
    end

    def amount_stat(params = {})
      res = select("sum(contract_amount) as total_contract, sum(actual_amount) as total_actual")[0]
      {
        contract_amount: res.total_contract.to_f,
        actual_amount: res.total_actual.to_f,
        unpaid_amount: res.total_contract.to_f - res.total_actual.to_f
      }
    end

    def group_stat(params = {})
      {
        patent_type: joins(:patent).group('inno_patents.patent_type').count,
        department: joins(patent: :departments).group("departments.name").count
      }
    end

    def month_amount_trend(params = {})
      start_date = Date.parse(params[:start_date]) rescue (Date.today - 5.months).beginning_of_month
      end_date = Date.parse(params[:end_date]) rescue Date.tomorrow

      select("
        to_char(inno_transforms.updated_at, 'YYYY年MM') as month,
        sum(contract_amount) as total_contract,
        sum(actual_amount) as total_actual
      ").where(updated_at: start_date...end_date)
        .group('month')
        .order('month')
        .map do |record|
          {
            month: record.month,
            total_contract: record.total_contract.to_f,
            total_actual: record.total_actual.to_f,
            total_unpaid: record.total_contract.to_f - record.total_actual.to_f
          }
        end
    end

    def month_num_trend(params = {})
      start_date = Date.parse(params[:start_date]) rescue (Date.today - 5.months).beginning_of_month
      end_date = Date.parse(params[:end_date]) rescue Date.tomorrow
    
      res = select("
        to_char(inno_transforms.updated_at, 'YYYY年MM') as month,
        count(CASE WHEN inno_patents.patent_type = 'design' THEN 1 END) as design_num,
        count(CASE WHEN inno_patents.patent_type = 'utility_model' THEN 1 END) as utility_model_num,
        count(CASE WHEN inno_patents.patent_type = 'invention' THEN 1 END) as invention_num,
        count(*) as patent_num
      ").joins(
        "left join inno_patents on inno_patents.id = inno_transforms.source_id and inno_transforms.source_type = 'Inno::Patent'"
      ).where(updated_at: start_date..end_date)
        .group('month')
        .order('month')
        .map do |record|
          [record.month, {
            design_num: record.design_num.to_i,
            utility_model_num: record.utility_model_num.to_i,
            invention_num: record.invention_num.to_i,
            patent_num: record.patent_num.to_i
          }]
        end.to_h

      months_between(start_date, end_date).map do |month|
        (res[month] || { design_num: 0, utility_model_num: 0, invention_num: 0, patent_num: 0 }).merge(month: month)
      end
    end

    def months_between(start_date, end_date)
      start_date, end_date = [start_date, end_date].sort
      months = []
      current_date = start_date.beginning_of_month
      while current_date <= end_date
        months << current_date.strftime('%Y年%m')
        current_date = current_date.next_month
      end
      months
    end
  end
end
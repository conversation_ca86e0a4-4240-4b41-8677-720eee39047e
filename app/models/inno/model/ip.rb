module Inno::Model::Ip
  extend ActiveSupport::Concern

  included do

    attribute :ip_state, :string, comment: '知识产权状态'

    enum ip_state: {
      maintain: 'maintain',
      invalid: 'invalid',
      review: 'review',
      transfer: 'transfer'
    }, _prefix: true

  end

  class_methods do

    def ip_state_stat(params={})
      group(:ip_state).count
    end

    def transform_state_stat(params={})
      group(:transform_state).count
    end

  end
end
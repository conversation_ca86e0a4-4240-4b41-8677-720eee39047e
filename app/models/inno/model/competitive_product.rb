module Inno::Model::CompetitiveProduct
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :creator, class_name: '::User'
    belongs_to :source, polymorphic: true

    attribute :name, :string, comment: '名称'
    attribute :company_name, :string, comment: '生产企业'
    attribute :approval_no, :string, comment: '批准文号'
    attribute :intro, :text, comment: '简介'

    formable

    default_value_for(:app) { |cp| cp.creator&.app }

    has_paper_trail_version(
      version_belongs: [:source],
      meta: {
        model_info: :version_model_info
      }
    )

    def version_model_info
      {
        name: name,
        type: class_name,
      }
    end
  end

  class_methods do
  end

end

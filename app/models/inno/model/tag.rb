module Inno::Model::Tag
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    treeable

    acts_as_list scope: [:app_id, :type]

    belongs_to :app

    attribute :type, :string, comment: 'STI类型', index: true
    attribute :name, :string, comment: '标签名称'
    attribute :position, :integer, comment: '排序'
    attribute :color, :string, comment: '标签颜色设置'

    default_value_for(:app) { |o| o.source.try(:app) }

    def ancestry_name
      ancestors&.pluck(:name)&.join('/')
    end
  end

  class_methods do
  end
end

class Inno::TagAction < ApplicationRecord
  include ActionCore::Model::Action

  # has_paper_trail_version(
  #   on: [:create, :destroy],
  #   version_belongs: [:user, :target],
  #   meta: {
  #     model_info: :version_model_info,
  #   },
  #   if: proc { |t| %w[source].include?(t.action_type) },
  # )

  # def version_model_info
  #   {
  #     action_type: action_type,
  #     target: target,
  #   }
  # end
end

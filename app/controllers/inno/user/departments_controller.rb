class Inno::User::DepartmentsController < SimpleController::BaseController
  defaults(
    resource_class: Department,
    collection_name: 'departments',
    instance_name: 'department',
    view_path: 'departments',
  )
  auth_action :user

  protected

  def after_association_chain(association)
    mode = params[:sub_q][:mode].presence if params[:sub_q].present?
    mode ||= params[:q][:mode].presence if params[:q].present?
    mode ||= params[:mode].presence

    if mode == 'managed'
      association = association.manage_by(current_user)
    else
      association = association.where(org_id: current_user.org_ids)
    end

    association
  end
end

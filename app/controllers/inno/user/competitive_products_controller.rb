class Inno::User::CompetitiveProductsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::CompetitiveProduct,
    collection_name: 'competitive_products',
    instance_name: 'competitive_product',
    view_path: 'inno/competitive_products',
  )
  auth_action :user

  private

  def create_competitive_product_params
    update_competitive_product_params.merge(
      creator: current_user
    )
  end

  def update_competitive_product_params
    params.require(:competitive_product).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :company_name,
      :approval_no,
      :intro,
      model_payload: {},
    )
  end
end

class Inno::User::WorksController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Work,
    collection_name: 'works',
    instance_name: 'work',
    view_path: 'inno/works',
  )
  include Disk::Controller::Diskable
  include Favor::Controller::Markable
  include Com::Controller::Versionable
  auth_action :user

  def statistic
    statistic_method = params[:statistic_method]

    unless %w(
      ip_state_stat
      transform_state_stat
    ).include?(statistic_method)
      raise ActiveRecord::RecordNotFound
    end

    render json: after_of_association_chain.distinct.send(statistic_method, params.merge(user: current_user))
  end

  def distribute
    ownership = resource.ownerships.find_or_initialize_by(distribute_params)
    if ownership.new_record?
      ownership.role = 'edit'
      ownership.save!
    end
    respond_resource
  end

  protected

  def begin_of_association_chain
    current_user.app
  end

  def method_for_association_chain
    :inno_works
  end

  private

  def distribute_params
    params.permit(:department_id, :user_id)
  end

  def create_work_params
    update_work_params.merge(creator: current_user)
  end

  def update_work_params
    params.require(:work).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :serial_no,
      :category,
      :maker,
      :owner,
      :completion_date,
      :pub_date,
      :agency,
      :reg_date,
      :reg_no,
      :apply_date,
      :apply_identity,
      :obtain_rights_way,
      :rights_belong_way,
      :contact_name,
      :contact_mobile,
      :convert_intention,
      :convert_way,
      :pub_state,
      payload: {},
      relate_department_ids: [],
    )
  end
end

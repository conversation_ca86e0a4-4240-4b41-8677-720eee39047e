class Inno::User::TransformItemsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::TransformItem,
    collection_name: 'transform_items',
    instance_name: 'transform_item',
    view_path: 'inno/transform_items',
  )
  auth_action :user

  belongs_to :transform


  private

  def create_transform_item_params
    update_transform_item_params.merge(
      creator: current_user
    )
  end

  def update_transform_item_params
    params.require(:transform_item).permit(
      *resource_class.try(:extra_permitted_attributes),
      :actual_amount,
      payload: {},
    )
  end
end

class Inno::User::SettingsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Setting,
    collection_name: 'settings',
    instance_name: 'setting',
    view_path: 'inno/settings',
  )

  auth_action :user

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :inno_settings
  end

  private

  def setting_params
    params.require(:setting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :state,
      :position,
      option: {},
    )
  end
end

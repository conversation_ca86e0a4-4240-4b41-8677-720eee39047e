class Inno::User::PaymentsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Payment,
    collection_name: 'payments',
    instance_name: 'payment',
    view_path: 'inno/payments',
  )

  auth_action :user
  belongs_to :patent, collection_name: :inno_patents

  protected

  def begin_of_association_chain
    current_app
  end

  private

  def create_payment_params
    update_payment_params.merge(user: current_user)
  end

  def update_payment_params
    params.require(:payment).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :patent_id,
      :type,
      :seq,
      :start_date,
      :end_date,
      :name,
      :state,
      :amount,
      :paid_at,
      :paid_type,
      attachment: {},
      payload: {},
    )
  end
end

class Inno::User::AwardsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Award,
    collection_name: 'awards',
    instance_name: 'award',
    view_path: 'inno/awards',
  )
  auth_action :user

  private

  def create_award_params
    update_award_params.merge(creator: current_user)
  end

  def update_award_params
    params.require(:award).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :year,
      :grant_unit,
      :level,
      :completer,
      :position,
    )
  end
end

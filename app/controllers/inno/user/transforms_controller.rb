class Inno::User::TransformsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Transform,
    collection_name: 'transforms',
    instance_name: 'transform',
    view_path: 'inno/transforms',
  )
  include State::Controller::Eventable

  auth_action :user

  def statistic
    statistic_method = params[:statistic_method]

    unless %w(
      transaction_stat
      month_amount_trend
      month_num_trend
      amount_stat
      group_stat
    ).include?(statistic_method)
      raise ActiveRecord::RecordNotFound
    end

    render json: after_of_association_chain.send(statistic_method, params.merge(user: current_user))
  end

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :inno_transforms
  end

  def after_association_chain(association)
    mode = params[:sub_q][:mode].presence if params[:sub_q].present?
    mode ||= params[:q][:mode].presence if params[:q].present?
    mode ||= params[:mode].presence
    
    if mode == 'managed'
      association = association.manage_by(current_user)
    end

    association
  end

  private

  def create_transform_params
    update_transform_params.merge(creator: current_user)
  end

  def update_transform_params
    params.require(:transform).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      payload: {},
    )
  end
end

class Inno::User::SoftwareCopyrightsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::SoftwareCopyright,
    collection_name: 'software_copyrights',
    instance_name: 'software_copyright',
    view_path: 'inno/software_copyrights',
  )
  include Disk::Controller::Diskable
  include Favor::Controller::Markable
  include Com::Controller::Versionable
  auth_action :user

  def statistic
    statistic_method = params[:statistic_method]

    unless %w(
      ip_state_stat
      transform_state_stat
    ).include?(statistic_method)
      raise ActiveRecord::RecordNotFound
    end

    render json: after_of_association_chain.distinct.send(statistic_method, params.merge(user: current_user))
  end

  def distribute
    ownership = resource.ownerships.find_or_initialize_by(distribute_params)
    if ownership.new_record?
      ownership.role = 'edit'
      ownership.save!
    end
    respond_resource
  end

  protected

  def begin_of_association_chain
    current_user.app
  end

  def method_for_association_chain
    :inno_software_copyrights
  end

  private

  def distribute_params
    params.permit(:department_id, :user_id)
  end

  def create_software_copyright_params
    update_software_copyright_params.merge(creator: current_user)
  end

  def update_software_copyright_params
    params.require(:software_copyright).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :serial_no,
      :reg_no,
      :owner,
      :right_way,
      :right_range,
      :cert_no,
      :short_name,
      :version_no,
      :member_names,
      :grant_date,
      :apply_identity,
      :apply_date,
      :apply_way,
      :soft_kind,
      :complete_date,
      :pub_state,
      :contact_name,
      :contact_mobile,
      :convert_intention,
      :convert_way,
      payload: {},
      relate_department_ids: [],
    )
  end
end

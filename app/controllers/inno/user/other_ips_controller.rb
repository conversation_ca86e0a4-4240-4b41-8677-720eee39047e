class Inno::User::OtherIpsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::OtherIp,
    collection_name: 'other_ips',
    instance_name: 'other_ip',
    view_path: 'inno/other_ips',
  )
  include Disk::Controller::Diskable
  include Favor::Controller::Markable
  include Com::Controller::Versionable
  auth_action :user

  def distribute
    ownership = resource.ownerships.find_or_initialize_by(distribute_params)
    if ownership.new_record?
      ownership.role = 'edit'
      ownership.save!
    end
    respond_resource
  end

  protected

  def begin_of_association_chain
    current_user.app
  end

  def method_for_association_chain
    :inno_other_ips
  end

  private

  def distribute_params
    params.permit(:department_id, :user_id)
  end

  def create_other_ip_params
    update_other_ip_params.merge(creator: current_user)
  end

  def update_other_ip_params
    params.require(:other_ip).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :kind,
      :grant_date,
      :apply_date,
      :apply_no,
      :cert_no,
      :obligee,
      :inventor,
      :agency,
      :contact_name,
      :contact_mobile,
      :convert_intention,
      :convert_way,
      payload: {},
      relate_department_ids: [],
    )
  end
end

class Inno::User::FundsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Fund,
    collection_name: 'funds',
    instance_name: 'fund',
    view_path: 'inno/funds',
  )

  auth_action :user

  protected

  def after_association_chain association
    @amount_info = Inno::Fund.amount_info(association)
    association
  end

  private

  def create_fund_params
    update_fund_params.merge(
      creator: current_user,
    )
  end

  def update_fund_params
    params.require(:fund).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :project_id,
      :creator_id,
      :seq,
      :planned_at,
      :is_planned,
      :name,
      :state,
      :amount,
      :position,
      :received_amount,
      :received_date,
      :cost_amount,
      :income_amount,
      :profit_amount,
      :tech_operator_amount,
      :tech_talent_amount,
      :group_assign_amount,
      :paid_unit,
      :paid_account,
      :paid_bank,
      :paid_date,
      :paid_num,
      :paid_ticket_date,
      :group_user_count,
      payload: {},
      attachment: {},
    )
  end
end

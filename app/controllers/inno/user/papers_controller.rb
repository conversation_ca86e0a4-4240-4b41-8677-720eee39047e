class Inno::User::PapersController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Paper,
    collection_name: 'papers',
    instance_name: 'paper',
    view_path: 'inno/papers',
  )
  auth_action :user

  private

  def create_paper_params
    update_paper_params.merge(
      creator: current_user
    )
  end

  def update_paper_params
    params.require(:paper).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :author,
      :journal_name,
      :jcr,
      :year,
      :pmid,
      :intro,
      :position,
    )
  end
end

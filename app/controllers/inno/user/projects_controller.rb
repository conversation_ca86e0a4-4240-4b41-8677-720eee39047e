class Inno::User::ProjectsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Project,
    collection_name: 'projects',
    instance_name: 'project',
    view_path: 'inno/projects',
  )

  auth_action :user

  def statistic
    projects = currrent_app.inno_projects.finished
    projects.order(start)
  end

  private

  def create_project_params
    project_params.merge(
      creator: current_user,
    )
  end

  def project_params
    params.require(:project).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :creator_id,
      :model_flag,
      :seq,
      :name,
      # :project_type,
      :leader_name,
      :contract_amount,
      :actual_amount,
      :state,
      :start_at,
      :end_at,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      payload: {},
      transform_type_project_tag_ids: [],
      transform_sub_type_project_tag_ids: [],
      project_relations_attributes: [
        :id,
        :_destroy,
        :source_type,
        :source_id,
      ]
    )
  end
end

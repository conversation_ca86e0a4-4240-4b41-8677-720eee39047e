class Inno::User::TeamMembersController < SimpleController::BaseController
  defaults(
    resource_class: Inno::TeamMember,
    collection_name: 'team_members',
    instance_name: 'team_member',
    view_path: 'inno/team_members',
  )
  auth_action :user

  def create
    Inno::TeamMember.transaction do
      if params_user_id
        user = current_user.app.users.find(params_user_id)
        user.update!(user_attributes_params[:user_attributes])

        end_of_association_chain.create!(
          create_team_member_params.merge(
            user_id: params_user_id
          )
        )
        render json: {}, status: 201
      elsif user_attributes_params[:user_attributes][:account].present?
        user = current_user.app.users.find_by(account: user_attributes_params[:user_attributes][:account])
        if user
          user.update!(user_attributes_params[:user_attributes])
          end_of_association_chain.create!(
            create_team_member_params.merge(
              user_id: user.id
            )
          )
          render json: {}, status: 201
        else
          # create a new user if not found
          new_user = current_user.app.users.create!(
            user_attributes_params[:user_attributes]
          )

          end_of_association_chain.create!(
            create_team_member_params.merge(
              user_id: new_user.id
            )
          )
          render json: {}, status: 201
        end
      else
        raise Error::BaseError.new(message: '请提供用户账号')
      end
    end
  end

  def update
    Inno::TeamMember.transaction do
      if params_user_id
        user = current_user.app.users.find(params_user_id)
        user.update!(user_attributes_params[:user_attributes])
        resource.update!(
          update_team_member_params.merge(
            user_id: params_user_id
          )
        )
        render json: {}, status: 201
      elsif user_attributes_params[:user_attributes][:account].present?
        user = current_user.app.users.find_by(account: user_attributes_params[:user_attributes][:account])
        if user
          user.update!(user_attributes_params[:user_attributes])
          resource.update!(
            update_team_member_params.merge(
              user_id: user.id
            )
          )
          render json: {}, status: 201
        else
          # create a new user if not found
          new_user = current_user.app.users.create!(
            user_attributes_params[:user_attributes]
          )

          resource.update!(
            update_team_member_params.merge(
              user_id: new_user.id
            )
          )
          render json: {}, status: 201
        end
      else
        resource.update!(update_team_member_params)
        render json: {}, status: 201
      end
    end
  end

  private

  def create_team_member_params
    update_team_member_params.merge(creator: current_user)
  end

  def update_team_member_params
    params.require(:team_member).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :user_id,
      # :department_id,
      :role,
      :position,
    )
  end

  def params_user_id
    params[:team_member][:user_attributes]&.[](:user_id)
  end

  def user_attributes_params
    params.require(:team_member).permit(
      user_attributes: [:account, :name, { model_payload: {}, avatar: {} }]
    )
  end
end

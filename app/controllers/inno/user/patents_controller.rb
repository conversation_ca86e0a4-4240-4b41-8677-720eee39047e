class Inno::User::PatentsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Patent,
    collection_name: 'patents',
    instance_name: 'patent',
    view_path: 'inno/patents',
  )
  include Disk::Controller::Diskable
  include Favor::Controller::Markable
  include Com::Controller::Versionable
  auth_action :user

  def statistic
    statistic_method = params[:statistic_method]

    unless %w(
      workbench_stat
      dashboard_stat
      year_pub_trend
      group_stat
      type_state_stat
      type_ip_state_stat
      type_transform_state_stat
    ).include?(statistic_method)
      raise ActiveRecord::RecordNotFound
    end

    render json: after_of_association_chain.distinct.send(statistic_method, params.merge(user: current_user))
  end

  def distribute
    ownership = resource.ownerships.find_or_initialize_by(distribute_params)
    if ownership.new_record?
      ownership.role = 'edit'
      ownership.save!
    end
    respond_resource
  end

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :inno_patents
  end

  def after_association_chain(association)
    mode = params[:sub_q][:mode].presence if params[:sub_q].present?
    mode ||= params[:q][:mode].presence if params[:q].present?
    mode ||= params[:mode].presence

    if mode == 'stared'
      association = association.star_by(current_user)
    elsif mode == 'managed'
      association = association.manage_by(current_user)
    end

    association
  end

  private

  def distribute_params
    params.permit(:department_id, :user_id)
  end

  def create_patent_params
    update_patent_params.merge(creator: current_user)
  end

  def update_patent_params
    params.require(:patent).permit(
      *resource_class.try(:extra_permitted_attributes),
      *current_user.extra_patent_permitted_attributes,
      :name,
      :apply_no,
      :apply_date,
      :pub_date,
      :pub_no,
      :abstract,
      :inventors,
      :ipc,
      :patent_type,
      :legal_state,
      :expire_date,
      :patentee,
      :contact_name,
      :contact_mobile,
      :agency,
      :agency_code,
      :patentee_state,
      :state,
      :convert_intention,
      :convert_way,
      :payment,
      :payment_end_date,
      :payment_start_date,
      payload: {},
      relate_department_ids: [],
    )
  end
end

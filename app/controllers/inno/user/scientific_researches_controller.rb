class Inno::User::ScientificResearchesController < SimpleController::BaseController
  defaults(
    resource_class: Inno::ScientificResearch,
    collection_name: 'scientific_researches',
    instance_name: 'scientific_research',
    view_path: 'inno/scientific_researches',
  )
  auth_action :user

  private

  def create_scientific_research_params
    update_scientific_research_params.merge(creator: current_user)
  end

  def update_scientific_research_params
    params.require(:scientific_research).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :num,
      :leader,
      :undertaking_unit,
      :level,
      :from,
      :kind,
      :init_at,
      :start_at,
      :end_at,
      :funds,
      :financial_support,
      :position,
      payload: {},
    )
  end
end

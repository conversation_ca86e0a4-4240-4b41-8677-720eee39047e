class Inno::User::OwnershipsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::Ownership,
    collection_name: 'ownerships',
    instance_name: 'ownership',
    view_path: 'inno/ownerships',
  )
  auth_action :user

  belongs_to :patent, collection_name: :inno_patents, optional: true
  belongs_to :software_copyright, collection_name: :inno_software_copyrights, optional: true
  belongs_to :work, collection_name: :inno_works, optional: true
  belongs_to :other_ip, collection_name: :inno_other_ips, optional: true

  def begin_of_association_chain
    current_user.app
  end

  private

  def ownership_params
    params.require(:ownership).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_type,
      :user_id,
      :role,
    )
  end
end

class Inno::User::ProjectTagsController < SimpleController::BaseController
  defaults(
    resource_class: Inno::ProjectTag,
    collection_name: 'project_tags',
    instance_name: 'project_tag',
    view_path: 'inno/project_tags',
  )

  auth_action :user

  private

  def project_tag_params
    params.require(:project_tag).permit(
      *resource_class.try(:extra_permitted_attributes),
      :app_id,
      :model_flag,
      :ancestry,
      :depth,
      :children_count,
      :type,
      :name,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end
end
